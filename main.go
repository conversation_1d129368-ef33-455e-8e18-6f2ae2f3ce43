package main

import (
	"bytes"
	"context"
	"fmt"
	"log"
	"math"
	"net/http"
	_ "net/http/pprof"
	"strconv"
	"strings"
	"sync"
	"sync/atomic"
	"time"

	"github.com/panjf2000/gnet/v2"
	"go.uber.org/zap"
	"go.uber.org/zap/zapcore"
)

const (
	// Connection timeout duration (30 seconds)
	connectionTimeout = 30 * time.Second
	// Cleanup interval for checking timeouts (every 5 seconds)
	cleanupInterval = 5 * time.Second
	// UI update interval (every 1 second)
	uiUpdateInterval = 1 * time.Second

	// Performance optimization constants
	maxHeaderSize  = 8192  // Maximum HTTP header size to process
	maxRequestSize = 16384 // Maximum request size to prevent DoS

	// Connection map sharding constants
	numShards = 256           // Power of 2 for efficient modulo operations
	shardMask = numShards - 1 // Bitmask for fast modulo

	// Pre-computed byte constants for faster comparisons
	spaceChar = byte(' ')
	crChar    = byte('\r')
	lfChar    = byte('\n')
	colonChar = byte(':')
	tabChar   = byte('\t')
)

var (
	blankResponse   = []byte("HTTP/1.1 200 OK\r\nContent-Length: 0\r\n\r\n")
	pathNginxStatus = []byte("/nginx_status")
	pathStatus      = []byte("/status")
	doubleCRLF      = []byte("\r\n\r\n")
	hostHeader      = []byte("host:")

	// Pre-defined subdomain patterns for zero-copy matching
	jsSubdomain      = []byte("js.relayed.network")
	uamSubdomain     = []byte("uam.relayed.network")
	managedSubdomain = []byte("managed.relayed.network")

	// High-performance structured logger
	logger *zap.Logger
)

var (
	// Optimized pools with larger initial capacities to reduce reallocations
	statusHeaderPool = sync.Pool{
		New: func() interface{} {
			return make([]byte, 0, 256) // Increased from 128
		},
	}
	statusBodyPool = sync.Pool{
		New: func() interface{} {
			return make([]byte, 0, 512) // Increased from 256
		},
	}

	// Pool for connection info to reduce allocations
	connInfoPool = sync.Pool{
		New: func() interface{} {
			return &ConnectionInfo{}
		},
	}

	// Pool for slice of connections to close (used in OnTick)
	connSlicePool = sync.Pool{
		New: func() interface{} {
			return make([]gnet.Conn, 0, 16) // Pre-allocate for 16 connections
		},
	}
)

// ConnectionInfo holds connection metadata for timeout tracking
type ConnectionInfo struct {
	lastActivity   int64 // Atomically updated Unix nano timestamp
	conn           gnet.Conn
	subdomainIndex int // To track which subdomain this connection belongs to
	// Timing wheel fields
	bucketIndex int             // Which bucket this connection is in
	next        *ConnectionInfo // Next connection in the bucket (linked list)
	prev        *ConnectionInfo // Previous connection in the bucket (linked list)
}

// TimingWheelBucket represents a single bucket in the timing wheel
type TimingWheelBucket struct {
	head *ConnectionInfo // Head of the doubly-linked list
	tail *ConnectionInfo // Tail of the doubly-linked list
}

// TimingWheel implements an efficient O(1) timeout mechanism
type TimingWheel struct {
	buckets     []TimingWheelBucket
	bucketCount int
	tickSize    time.Duration // Duration each bucket represents
	currentTick int64         // Current tick position
	mu          sync.RWMutex  // Protects the wheel structure
}

// ConnShard represents a single shard of the connection tracker
type ConnShard struct {
	mu    sync.RWMutex
	conns map[int]*ConnectionInfo
}

// ShardedConnTracker implements a sharded connection tracker for better concurrency
type ShardedConnTracker struct {
	shards [numShards]ConnShard
}

// NewTimingWheel creates a new timing wheel for efficient timeout management
func NewTimingWheel(tickSize time.Duration, bucketCount int) *TimingWheel {
	return &TimingWheel{
		buckets:     make([]TimingWheelBucket, bucketCount),
		bucketCount: bucketCount,
		tickSize:    tickSize,
		currentTick: time.Now().UnixNano() / int64(tickSize),
	}
}

// addConnection adds a connection to the appropriate bucket
func (tw *TimingWheel) addConnection(conn *ConnectionInfo, expireTime time.Time) {
	tw.mu.Lock()
	defer tw.mu.Unlock()

	// Remove from old bucket if already in wheel
	if conn.bucketIndex != -1 {
		tw.removeConnectionUnsafe(conn)
	}
	tw.addConnectionUnsafe(conn, expireTime)
}

// addConnectionUnsafe adds a connection to a bucket without acquiring a lock.
// It assumes the caller holds the lock.
func (tw *TimingWheel) addConnectionUnsafe(conn *ConnectionInfo, expireTime time.Time) {
	// Calculate which bucket this connection should go in
	expireTick := expireTime.UnixNano() / int64(tw.tickSize)
	bucketIndex := int(expireTick % int64(tw.bucketCount))

	// Add to new bucket
	conn.bucketIndex = bucketIndex
	bucket := &tw.buckets[bucketIndex]

	// Add to head of doubly-linked list
	conn.next = bucket.head
	conn.prev = nil
	if bucket.head != nil {
		bucket.head.prev = conn
	} else {
		bucket.tail = conn
	}
	bucket.head = conn
}

// removeConnectionUnsafe removes a connection from its bucket (must hold lock)
func (tw *TimingWheel) removeConnectionUnsafe(conn *ConnectionInfo) {
	if conn.bucketIndex == -1 {
		return // Not in wheel
	}

	bucket := &tw.buckets[conn.bucketIndex]

	// Remove from doubly-linked list
	if conn.prev != nil {
		conn.prev.next = conn.next
	} else {
		bucket.head = conn.next
	}

	if conn.next != nil {
		conn.next.prev = conn.prev
	} else {
		bucket.tail = conn.prev
	}

	// Clear connection's wheel fields
	conn.bucketIndex = -1
	conn.next = nil
	conn.prev = nil
}

// removeConnection removes a connection from the wheel
func (tw *TimingWheel) removeConnection(conn *ConnectionInfo) {
	tw.mu.Lock()
	defer tw.mu.Unlock()
	tw.removeConnectionUnsafe(conn)
}

// tick advances the wheel and returns expired connections
func (tw *TimingWheel) tick() []*ConnectionInfo {
	tw.mu.Lock()
	defer tw.mu.Unlock()

	now := time.Now()
	currentTick := now.UnixNano() / int64(tw.tickSize)

	var expired []*ConnectionInfo

	// Process all ticks that have passed since last check
	for tw.currentTick < currentTick {
		tw.currentTick++
		bucketIndex := int(tw.currentTick % int64(tw.bucketCount))
		bucket := &tw.buckets[bucketIndex]

		// Collect all connections in this bucket that are actually expired
		conn := bucket.head
		for conn != nil {
			next := conn.next
			lastActivityNanos := atomic.LoadInt64(&conn.lastActivity)
			lastActivityTime := time.Unix(0, lastActivityNanos)

			// Check if connection is actually expired (double-check due to wheel granularity)
			if now.Sub(lastActivityTime) >= connectionTimeout {
				expired = append(expired, conn)
				tw.removeConnectionUnsafe(conn)
			} else {
				// Connection not expired yet, re-add to appropriate bucket
				expireTime := lastActivityTime.Add(connectionTimeout)
				tw.removeConnectionUnsafe(conn)
				tw.addConnectionUnsafe(conn, expireTime)
			}

			conn = next
		}
	}

	return expired
}

// NewShardedConnTracker creates a new sharded connection tracker
func NewShardedConnTracker() *ShardedConnTracker {
	tracker := &ShardedConnTracker{}
	for i := 0; i < numShards; i++ {
		tracker.shards[i].conns = make(map[int]*ConnectionInfo)
	}
	return tracker
}

// getShard returns the shard for a given file descriptor
func (sct *ShardedConnTracker) getShard(fd int) *ConnShard {
	return &sct.shards[fd&shardMask]
}

// Store stores a connection info in the appropriate shard
func (sct *ShardedConnTracker) Store(fd int, connInfo *ConnectionInfo) {
	shard := sct.getShard(fd)
	shard.mu.Lock()
	shard.conns[fd] = connInfo
	shard.mu.Unlock()
}

// Load loads a connection info from the appropriate shard
func (sct *ShardedConnTracker) Load(fd int) (*ConnectionInfo, bool) {
	shard := sct.getShard(fd)
	shard.mu.RLock()
	connInfo, ok := shard.conns[fd]
	shard.mu.RUnlock()
	return connInfo, ok
}

// LoadAndDelete loads and deletes a connection info from the appropriate shard
func (sct *ShardedConnTracker) LoadAndDelete(fd int) (*ConnectionInfo, bool) {
	shard := sct.getShard(fd)
	shard.mu.Lock()
	connInfo, ok := shard.conns[fd]
	if ok {
		delete(shard.conns, fd)
	}
	shard.mu.Unlock()
	return connInfo, ok
}

// Range iterates over all connections in all shards
func (sct *ShardedConnTracker) Range(fn func(fd int, connInfo *ConnectionInfo) bool) {
	for i := 0; i < numShards; i++ {
		shard := &sct.shards[i]
		shard.mu.RLock()
		for fd, connInfo := range shard.conns {
			if !fn(fd, connInfo) {
				shard.mu.RUnlock()
				return
			}
		}
		shard.mu.RUnlock()
	}
}

// SubdomainStats holds statistics for a specific subdomain
type SubdomainStats struct {
	connections int64
	accepts     int64
	handled     int64
	requests    int64
	reading     int64
	// RPS tracking
	lastRequests int64
	currentRPS   int64
}

// Subdomain indices for array-based lookup (faster than map)
const (
	subdomainJS = iota
	subdomainUAM
	subdomainManaged
	subdomainDefault
	subdomainCount
)

// Subdomain names for display
var subdomainNames = [subdomainCount]string{
	subdomainJS:      "js.relayed.network",
	subdomainUAM:     "uam.relayed.network",
	subdomainManaged: "managed.relayed.network",
	subdomainDefault: "default/other",
}

type rpsServer struct {
	gnet.BuiltinEventEngine
	addr      string
	multicore bool
	eng       gnet.Engine
	// Global stats
	connections int64
	// Pre-allocated per-subdomain stats (no map lookups, no mutex needed)
	subdomainStats [subdomainCount]SubdomainStats
	// Connection tracking for timeouts - now using sharded map for better concurrency
	connTracker *ShardedConnTracker
	// Timing wheel for O(1) timeout management
	timingWheel *TimingWheel
	// UI state
	startTime    time.Time
	uiTicker     *time.Ticker
	lastUIUpdate time.Time
	uiCancel     context.CancelFunc
	// PGO profiling server
	pprofServer *http.Server
}

// extractSubdomainIndex extracts subdomain index from host header value using optimized byte operations
func extractSubdomainIndex(hostValue []byte) int {
	// Remove port if present (find colon) - optimized with direct byte comparison
	for i, b := range hostValue {
		if b == colonChar {
			hostValue = hostValue[:i]
			break
		}
	}

	// Fast length-based pre-filtering before byte comparison
	hostLen := len(hostValue)
	switch hostLen {
	case len(jsSubdomain):
		if bytes.Equal(hostValue, jsSubdomain) {
			return subdomainJS
		}
	case len(uamSubdomain):
		if bytes.Equal(hostValue, uamSubdomain) {
			return subdomainUAM
		}
	case len(managedSubdomain):
		if bytes.Equal(hostValue, managedSubdomain) {
			return subdomainManaged
		}
	}

	// Default for unknown hosts
	return subdomainDefault
}

func (rs *rpsServer) OnBoot(eng gnet.Engine) (action gnet.Action) {
	rs.eng = eng
	rs.startTime = time.Now()
	rs.lastUIUpdate = time.Now()

	// Initialize sharded connection tracker
	rs.connTracker = NewShardedConnTracker()

	// Initialize timing wheel for O(1) timeout management
	// Use 1-second buckets with enough buckets to cover 2x the timeout period
	wheelBuckets := int(connectionTimeout.Seconds()) * 2
	rs.timingWheel = NewTimingWheel(time.Second, wheelBuckets)

	// Start pprof server for PGO profiling on a separate port
	rs.pprofServer = &http.Server{
		Addr: ":6060",
	}
	go func() {
		logger.Info("Starting pprof server for PGO profiling", zap.String("addr", ":6060"))
		if err := rs.pprofServer.ListenAndServe(); err != nil && err != http.ErrServerClosed {
			logger.Error("pprof server error", zap.Error(err))
		}
	}()

	// Start UI update goroutine with context for graceful shutdown
	ctx, cancel := context.WithCancel(context.Background())
	rs.uiCancel = cancel
	go rs.runUI(ctx)

	// No need to initialize subdomainStats array - zero values are fine
	return
}

func (rs *rpsServer) OnOpen(c gnet.Conn) (out []byte, action gnet.Action) {
	atomic.AddInt64(&rs.connections, 1)

	// Track connection for timeout management using pool to reduce allocations
	now := time.Now()
	connInfo := connInfoPool.Get().(*ConnectionInfo)
	atomic.StoreInt64(&connInfo.lastActivity, now.UnixNano())
	connInfo.conn = c
	connInfo.subdomainIndex = -1 // -1 indicates unknown subdomain
	connInfo.bucketIndex = -1    // Initialize timing wheel fields
	connInfo.next = nil
	connInfo.prev = nil

	rs.connTracker.Store(c.Fd(), connInfo)

	// Add to timing wheel for efficient timeout tracking
	expireTime := now.Add(connectionTimeout)
	rs.timingWheel.addConnection(connInfo, expireTime)

	return
}

func (rs *rpsServer) OnClose(c gnet.Conn, err error) (action gnet.Action) {
	atomic.AddInt64(&rs.connections, -1)

	// Remove connection from tracker and return to pool
	if connInfo, ok := rs.connTracker.LoadAndDelete(c.Fd()); ok {
		// Remove from timing wheel
		rs.timingWheel.removeConnection(connInfo)

		// Decrement subdomain connection count if it was assigned
		if connInfo.subdomainIndex != -1 {
			atomic.AddInt64(&rs.subdomainStats[connInfo.subdomainIndex].connections, -1)
		}

		// Clear the connection info before returning to pool
		connInfo.conn = nil
		connInfo.bucketIndex = -1
		connInfo.next = nil
		connInfo.prev = nil
		connInfoPool.Put(connInfo)
	}

	return
}

func (rs *rpsServer) OnTraffic(c gnet.Conn) (action gnet.Action) {
	// Update last activity time for timeout tracking
	var connInfo *ConnectionInfo
	if info, ok := rs.connTracker.Load(c.Fd()); ok {
		connInfo = info
		now := time.Now()
		atomic.StoreInt64(&connInfo.lastActivity, now.UnixNano())

		// Update timing wheel with new expiry time
		expireTime := now.Add(connectionTimeout)
		rs.timingWheel.addConnection(connInfo, expireTime)
	}

	// Peek(-1) returns all bytes in the ring-buffer without copying. This is a zero-copy operation.
	buf, err := c.Peek(-1)
	if err != nil {
		return gnet.Close
	}

	// Early size check to prevent processing oversized requests
	if len(buf) > maxRequestSize {
		return gnet.Close
	}

	// Find the end of the HTTP request, marked by a double CRLF.
	idx := bytes.Index(buf, doubleCRLF)
	if idx < 0 {
		// Incomplete request, wait for more data.
		return gnet.None
	}
	requestLen := idx + len(doubleCRLF)

	// Defer the discard operation. It will be executed on all return paths from this point forward,
	// ensuring the processed data is removed from the buffer before the connection is closed.
	defer func() {
		_, _ = c.Discard(requestLen)
	}()

	// Create a slice that views the request data from the buffer. This is also a zero-copy operation.
	data := buf[:requestLen]

	// Parse HTTP request to extract path and host - optimized parsing
	lineEnd := bytes.IndexByte(data, lfChar)
	if lineEnd < 0 {
		c.Write(blankResponse)
		return gnet.Close
	}
	requestLine := data[:lineEnd]
	if len(requestLine) > 0 && requestLine[len(requestLine)-1] == crChar {
		requestLine = requestLine[:len(requestLine)-1]
	}

	// Extract path from request line
	firstSpace := bytes.IndexByte(requestLine, spaceChar)
	if firstSpace < 0 {
		c.Write(blankResponse)
		return gnet.Close
	}
	pathAndQuery := requestLine[firstSpace+1:]
	secondSpace := bytes.IndexByte(pathAndQuery, spaceChar)
	var path []byte
	if secondSpace < 0 {
		path = pathAndQuery
	} else {
		path = pathAndQuery[:secondSpace]
	}

	// Extract Host header using optimized zero-copy byte operations
	subdomainIndex := subdomainDefault
	headerStart := lineEnd + 1

	// Limit header parsing to prevent DoS attacks
	maxHeaderEnd := headerStart + maxHeaderSize
	if maxHeaderEnd > len(data) {
		maxHeaderEnd = len(data)
	}

	for headerStart < maxHeaderEnd {
		headerEnd := bytes.IndexByte(data[headerStart:maxHeaderEnd], lfChar)
		if headerEnd < 0 {
			break
		}
		headerEnd += headerStart

		headerLine := data[headerStart:headerEnd]
		if len(headerLine) > 0 && headerLine[len(headerLine)-1] == crChar {
			headerLine = headerLine[:len(headerLine)-1]
		}

		// Optimized case-insensitive comparison for "host:" header
		if len(headerLine) >= 5 && bytes.EqualFold(headerLine[:5], hostHeader) {

			// Extract host value (skip "host:" and trim spaces) - optimized trimming
			hostStart := 5
			for hostStart < len(headerLine) && (headerLine[hostStart] == spaceChar || headerLine[hostStart] == tabChar) {
				hostStart++
			}
			if hostStart < len(headerLine) {
				hostValue := headerLine[hostStart:]
				subdomainIndex = extractSubdomainIndex(hostValue)
			}
			break
		}

		headerStart = headerEnd + 1
	}

	// Update per-subdomain statistics using direct array access (no locks, no allocations)
	subStats := &rs.subdomainStats[subdomainIndex]
	atomic.AddInt64(&subStats.requests, 1)
	atomic.AddInt64(&subStats.reading, 1)
	defer atomic.AddInt64(&subStats.reading, -1)

	// Associate connection with subdomain if not already done
	if connInfo != nil && connInfo.subdomainIndex == -1 {
		connInfo.subdomainIndex = subdomainIndex
		atomic.AddInt64(&subStats.connections, 1)
	}

	// For nginx status compatibility, let's assume accepts/handled are per-request
	atomic.AddInt64(&subStats.accepts, 1)
	atomic.AddInt64(&subStats.handled, 1)

	if bytes.Equal(path, pathNginxStatus) || bytes.Equal(path, pathStatus) {
		// Use subdomain-specific statistics
		activeConnections := atomic.LoadInt64(&rs.subdomainStats[subdomainIndex].connections)
		reading := atomic.LoadInt64(&subStats.reading)
		waiting := activeConnections - reading
		if waiting < 0 {
			waiting = 0
		}

		subdomainAccepts := atomic.LoadInt64(&subStats.accepts)
		subdomainHandled := atomic.LoadInt64(&subStats.handled)
		subdomainRequests := atomic.LoadInt64(&subStats.requests)

		// build body in exact nginx status format
		body := statusBodyPool.Get().([]byte)
		body = body[:0]
		body = append(body, "Active connections: "...)
		body = strconv.AppendInt(body, activeConnections, 10)
		body = append(body, " \nserver accepts handled requests\n "...)
		body = strconv.AppendInt(body, subdomainAccepts, 10)
		body = append(body, ' ')
		body = strconv.AppendInt(body, subdomainHandled, 10)
		body = append(body, ' ')
		body = strconv.AppendInt(body, subdomainRequests, 10)
		body = append(body, " \nReading: "...)
		body = strconv.AppendInt(body, reading, 10)
		body = append(body, " Writing: "...)
		body = strconv.AppendInt(body, 0, 10) // gnet doesn't expose writing stats directly
		body = append(body, " Waiting: "...)
		body = strconv.AppendInt(body, waiting, 10)
		body = append(body, " \n"...)

		// build header
		header := statusHeaderPool.Get().([]byte)
		header = header[:0]
		header = append(header, "HTTP/1.1 200 OK\r\nContent-Type: text/plain\r\nContent-Length: "...)
		header = strconv.AppendInt(header, int64(len(body)), 10)
		header = append(header, "\r\n\r\n"...)

		c.Writev([][]byte{header, body})
		statusHeaderPool.Put(header)
		statusBodyPool.Put(body)
	} else {
		c.Write(blankResponse)
	}

	return gnet.Close
}

// runUI runs the text-based UI in a separate goroutine
func (rs *rpsServer) runUI(ctx context.Context) {
	ticker := time.NewTicker(uiUpdateInterval)
	defer ticker.Stop()

	// Clear screen and hide cursor
	fmt.Print("\033[2J\033[?25l")

	for {
		select {
		case <-ticker.C:
			rs.updateUI()
		case <-ctx.Done():
			return
		}
	}
}

// updateUI refreshes the console display using optimized buffered output
func (rs *rpsServer) updateUI() {
	now := time.Now()
	uptime := now.Sub(rs.startTime)

	// Calculate RPS for each subdomain
	for i := 0; i < subdomainCount; i++ {
		currentRequests := atomic.LoadInt64(&rs.subdomainStats[i].requests)
		lastRequests := atomic.LoadInt64(&rs.subdomainStats[i].lastRequests)
		rps := currentRequests - lastRequests
		atomic.StoreInt64(&rs.subdomainStats[i].currentRPS, rps)
		atomic.StoreInt64(&rs.subdomainStats[i].lastRequests, currentRequests)
	}

	// Use strings.Builder for efficient buffered output (optimization from plan)
	var builder strings.Builder
	builder.Grow(2048) // Pre-allocate buffer to reduce reallocations

	// Move cursor to top-left and clear screen
	builder.WriteString("\033[H\033[2J")

	// Header
	builder.WriteString("╔══════════════════════════════════════════════════════════════════════════════╗\n")
	builder.WriteString("║                            RPS Server Monitor                               ║\n")
	builder.WriteString("╠══════════════════════════════════════════════════════════════════════════════╣\n")
	fmt.Fprintf(&builder, "║ Uptime: %-20s │ Total Active Connections: %-10d │ Port: 9000    ║\n",
		rs.formatDuration(uptime), atomic.LoadInt64(&rs.connections))
	builder.WriteString("╠══════════════════════════════════════════════════════════════════════════════╣\n")
	builder.WriteString("║                              Subdomain Statistics                           ║\n")
	builder.WriteString("╠═══════════════════════════════╤═══════╤═══════════╤═══════╤═══════╤═══════════╣\n")
	builder.WriteString("║ Subdomain                     │  RPS  │ Requests  │ Active│ Read  │ Handled   ║\n")
	builder.WriteString("╠═══════════════════════════════╪═══════╪═══════════╪═══════╪═══════╪═══════════╣\n")

	// Subdomain stats
	for i := 0; i < subdomainCount; i++ {
		stats := &rs.subdomainStats[i]
		name := subdomainNames[i]
		if len(name) > 29 {
			name = name[:26] + "..."
		}

		rps := atomic.LoadInt64(&stats.currentRPS)
		total := atomic.LoadInt64(&stats.requests)
		connections := atomic.LoadInt64(&stats.connections)
		reading := atomic.LoadInt64(&stats.reading)
		handled := atomic.LoadInt64(&stats.handled)

		fmt.Fprintf(&builder, "║ %-29s │ %5d │ %9d │ %5d │ %5d │ %9d ║\n",
			name, rps, total, connections, reading, handled)
	}

	builder.WriteString("╚═══════════════════════════════╧═══════╧═══════════╧═══════╧═══════╧═══════════╝\n")
	builder.WriteString("\nPress Ctrl+C to stop the server\n")
	fmt.Fprintf(&builder, "Last updated: %s\n", now.Format("15:04:05"))

	// Single write operation to console (optimization from plan)
	fmt.Print(builder.String())
}

// formatDuration formats a duration in a human-readable way
func (rs *rpsServer) formatDuration(d time.Duration) string {
	if d < time.Minute {
		return fmt.Sprintf("%.0fs", d.Seconds())
	} else if d < time.Hour {
		return fmt.Sprintf("%.0fm %.0fs", d.Minutes(), math.Mod(d.Seconds(), 60))
	} else {
		hours := int(d.Hours())
		minutes := int(d.Minutes()) - hours*60
		return fmt.Sprintf("%dh %dm", hours, minutes)
	}
}

// OnTick implements O(1) timeout checking using timing wheel
func (rs *rpsServer) OnTick() (delay time.Duration, action gnet.Action) {
	// Use timing wheel to get expired connections in O(1) time
	expiredConnections := rs.timingWheel.tick()

	// Close timed-out connections with conditional logging for performance
	if len(expiredConnections) > 0 {
		// Only log if significant number of connections are being closed (reduces log spam)
		if len(expiredConnections) >= 10 {
			logger.Info("Closing timed-out connections",
				zap.Int("count", len(expiredConnections)),
				zap.Duration("timeout", connectionTimeout))
		}
		for _, connInfo := range expiredConnections {
			connInfo.conn.Close() // This will trigger OnClose which removes from tracker
		}
	}

	// Return cleanup interval for next tick
	return cleanupInterval, gnet.None
}

// Shutdown gracefully stops the UI goroutine and pprof server
func (rs *rpsServer) Shutdown() {
	if rs.uiCancel != nil {
		rs.uiCancel()
	}
	if rs.pprofServer != nil {
		ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
		defer cancel()
		rs.pprofServer.Shutdown(ctx)
	}
}

// initLogger initializes a high-performance structured logger
func initLogger() {
	// Create a high-performance production logger configuration
	config := zap.NewProductionConfig()

	// Configure for high throughput with async logging
	config.Level = zap.NewAtomicLevelAt(zap.InfoLevel)
	config.Sampling = &zap.SamplingConfig{
		Initial:    100, // Log first 100 messages per second
		Thereafter: 100, // Then log every 100th message
	}

	// Use JSON encoding for structured logs
	config.Encoding = "json"

	// Configure encoder for performance
	config.EncoderConfig = zapcore.EncoderConfig{
		TimeKey:        "ts",
		LevelKey:       "level",
		NameKey:        "logger",
		CallerKey:      "caller",
		MessageKey:     "msg",
		StacktraceKey:  "stacktrace",
		LineEnding:     zapcore.DefaultLineEnding,
		EncodeLevel:    zapcore.LowercaseLevelEncoder,
		EncodeTime:     zapcore.EpochTimeEncoder, // Use epoch time for performance
		EncodeDuration: zapcore.SecondsDurationEncoder,
		EncodeCaller:   zapcore.ShortCallerEncoder,
	}

	var err error
	logger, err = config.Build(
		zap.AddCaller(),
		zap.AddStacktrace(zap.ErrorLevel), // Only add stacktrace for errors
	)
	if err != nil {
		log.Fatalf("failed to initialize logger: %v", err)
	}
}

func main() {
	// Initialize high-performance logger first
	initLogger()
	defer logger.Sync() // Flush any buffered log entries

	rs := &rpsServer{
		addr:      "tcp://:9000",
		multicore: true,
	}

	// Setup graceful shutdown to restore cursor and stop UI goroutine
	defer func() {
		rs.Shutdown()       // Stop UI goroutine gracefully
		fmt.Print("[?25h") // Show cursor
		if r := recover(); r != nil {
			logger.Error("Server panic", zap.Any("panic", r), zap.Stack("stack"))
		}
	}()

	logger.Info("Starting RPS server", zap.String("address", rs.addr))
	logger.Info("Connection timeout", zap.Duration("timeout", connectionTimeout))
	logger.Info("Cleanup interval", zap.Duration("interval", cleanupInterval))
	logger.Info("Multicore mode", zap.Bool("enabled", rs.multicore))
	logger.Info("Connection tracker shards", zap.Int("count", numShards))
	logger.Info("UI will start after server boot...")

	// Performance optimizations for gnet
	options := []gnet.Option{
		gnet.WithMulticore(rs.multicore),
		gnet.WithReusePort(true),             // Enable SO_REUSEPORT for better load balancing
		gnet.WithTCPKeepAlive(time.Minute),   // Enable TCP keepalive
		gnet.WithTCPNoDelay(gnet.TCPNoDelay), // Disable Nagle's algorithm for lower latency
		gnet.WithSocketRecvBuffer(64 * 1024), // Increase receive buffer
		gnet.WithSocketSendBuffer(64 * 1024), // Increase send buffer
	}

	if err := gnet.Run(rs, rs.addr, options...); err != nil {
		logger.Fatal("gnet server failed to run", zap.Error(err))
	}
}
