package main

import (
	"bytes"
	"context"
	"crypto/rand"
	"crypto/sha256"
	"crypto/subtle"
	"encoding/hex"
	"encoding/json"
	"fmt"
	"log"
	"math"
	"net/http"
	_ "net/http/pprof"
	"os"
	"strconv"
	"strings"
	"sync"
	"sync/atomic"
	"time"

	"github.com/golang-jwt/jwt/v4"
	"github.com/panjf2000/gnet/v2"
	"go.uber.org/zap"
	"go.uber.org/zap/zapcore"
)

const (
	// Connection timeout duration (30 seconds)
	connectionTimeout = 30 * time.Second
	// Cleanup interval for checking timeouts (every 5 seconds)
	cleanupInterval = 5 * time.Second
	// UI update interval (every 1 second)
	uiUpdateInterval = 1 * time.Second

	// Performance optimization constants
	maxHeaderSize  = 8192  // Maximum HTTP header size to process
	maxRequestSize = 16384 // Maximum request size to prevent DoS

	// Connection map sharding constants
	numShards = 256           // Power of 2 for efficient modulo operations
	shardMask = numShards - 1 // Bitmask for fast modulo

	// Pre-computed byte constants for faster comparisons
	spaceChar = byte(' ')
	crChar    = byte('\r')
	lfChar    = byte('\n')
	colonChar = byte(':')
	tabChar   = byte('\t')
)

var (
	blankResponse   = []byte("HTTP/1.1 200 OK\r\nContent-Length: 0\r\n\r\n")
	pathNginxStatus = []byte("/nginx_status")
	pathStatus      = []byte("/status")
	doubleCRLF      = []byte("\r\n\r\n")
	hostHeader      = []byte("host:")

	// Pre-defined subdomain patterns for zero-copy matching
	jsSubdomain      = []byte("js.relayed.network")
	uamSubdomain     = []byte("uam.relayed.network")
	managedSubdomain = []byte("managed.relayed.network")

	// High-performance structured logger
	logger *zap.Logger
)

var (
	// Optimized pools with larger initial capacities to reduce reallocations
	statusHeaderPool = sync.Pool{
		New: func() interface{} {
			return make([]byte, 0, 256) // Increased from 128
		},
	}
	statusBodyPool = sync.Pool{
		New: func() interface{} {
			return make([]byte, 0, 512) // Increased from 256
		},
	}

	// Pool for connection info to reduce allocations
	connInfoPool = sync.Pool{
		New: func() interface{} {
			return &ConnectionInfo{}
		},
	}

	// Pool for slice of connections to close (used in OnTick)
	connSlicePool = sync.Pool{
		New: func() interface{} {
			return make([]gnet.Conn, 0, 16) // Pre-allocate for 16 connections
		},
	}
)

// Configuration structure for PoW system
type Config struct {
	Port                                int               `json:"port"`
	InitialDifficulty                   int               `json:"initial_difficulty"`
	ChallengeExpiryMinutes              int               `json:"challenge_expiry_minutes"`
	EnableDynamicDifficulty             bool              `json:"enable_dynamic_difficulty"`
	RateLimitPerMinute                  int               `json:"rate_limit_per_minute"`
	JWTSecretKey                        string            `json:"jwt_secret_key"`
	JWTCookieName                       string            `json:"jwt_cookie_name"`
	JWTExpiryHours                      int               `json:"jwt_expiry_hours"`
	MaxDifficulty                       int               `json:"max_difficulty"`
	MinDifficulty                       int               `json:"min_difficulty"`
	DifficultyAdjustmentIntervalMinutes int               `json:"difficulty_adjustment_interval_minutes"`
	TargetSolveTimeSeconds              int               `json:"target_solve_time_seconds"`
	MetricsEnabled                      bool              `json:"metrics_enabled"`
	SecurityHeaders                     map[string]string `json:"security_headers"`
}

// PoWChallenge represents a proof-of-work challenge
type PoWChallenge struct {
	Solution     string    `json:"solution"`
	CreationTime time.Time `json:"creation_time"`
	Difficulty   int       `json:"difficulty"`
}

// PoWShard represents a single shard of the PoW challenge store
type PoWShard struct {
	mu         sync.RWMutex
	challenges map[string]*PoWChallenge
}

// ShardedPoWStore implements a sharded PoW challenge store for better concurrency
type ShardedPoWStore struct {
	shards [numShards]PoWShard
}

// IPRateLimitShard represents a single shard of the IP rate limiting store
type IPRateLimitShard struct {
	mu       sync.RWMutex
	requests map[string]*IPRequestCount
}

// IPRequestCount tracks request counts per IP for rate limiting
type IPRequestCount struct {
	Count       int64     `json:"count"`
	WindowStart time.Time `json:"window_start"`
}

// ShardedIPRateLimit implements a sharded IP rate limiting store
type ShardedIPRateLimit struct {
	shards [numShards]IPRateLimitShard
}

// PoWMetrics holds metrics for the PoW system
type PoWMetrics struct {
	ChallengesIssued    int64   `json:"challenges_issued"`
	ChallengesVerified  int64   `json:"challenges_verified"`
	ChallengesFailed    int64   `json:"challenges_failed"`
	AverageSolveTime    float64 `json:"average_solve_time_seconds"`
	CurrentDifficulty   int     `json:"current_difficulty"`
	ActiveChallenges    int64   `json:"active_challenges"`
	RateLimitedRequests int64   `json:"rate_limited_requests"`
}

// JWT Claims structure
type PoWClaims struct {
	Subdomain string `json:"subdomain"`
	jwt.RegisteredClaims
}

// ConnectionInfo holds connection metadata for timeout tracking
type ConnectionInfo struct {
	lastActivity   int64 // Atomically updated Unix nano timestamp
	conn           gnet.Conn
	subdomainIndex int // To track which subdomain this connection belongs to
	// Timing wheel fields
	bucketIndex int             // Which bucket this connection is in
	next        *ConnectionInfo // Next connection in the bucket (linked list)
	prev        *ConnectionInfo // Previous connection in the bucket (linked list)
}

// TimingWheelBucket represents a single bucket in the timing wheel
type TimingWheelBucket struct {
	head *ConnectionInfo // Head of the doubly-linked list
	tail *ConnectionInfo // Tail of the doubly-linked list
}

// TimingWheel implements an efficient O(1) timeout mechanism
type TimingWheel struct {
	buckets     []TimingWheelBucket
	bucketCount int
	tickSize    time.Duration // Duration each bucket represents
	currentTick int64         // Current tick position
	mu          sync.RWMutex  // Protects the wheel structure
}

// ConnShard represents a single shard of the connection tracker
type ConnShard struct {
	mu    sync.RWMutex
	conns map[int]*ConnectionInfo
}

// ShardedConnTracker implements a sharded connection tracker for better concurrency
type ShardedConnTracker struct {
	shards [numShards]ConnShard
}

// NewTimingWheel creates a new timing wheel for efficient timeout management
func NewTimingWheel(tickSize time.Duration, bucketCount int) *TimingWheel {
	return &TimingWheel{
		buckets:     make([]TimingWheelBucket, bucketCount),
		bucketCount: bucketCount,
		tickSize:    tickSize,
		currentTick: time.Now().UnixNano() / int64(tickSize),
	}
}

// addConnection adds a connection to the appropriate bucket
func (tw *TimingWheel) addConnection(conn *ConnectionInfo, expireTime time.Time) {
	tw.mu.Lock()
	defer tw.mu.Unlock()

	// Remove from old bucket if already in wheel
	if conn.bucketIndex != -1 {
		tw.removeConnectionUnsafe(conn)
	}
	tw.addConnectionUnsafe(conn, expireTime)
}

// addConnectionUnsafe adds a connection to a bucket without acquiring a lock.
// It assumes the caller holds the lock.
func (tw *TimingWheel) addConnectionUnsafe(conn *ConnectionInfo, expireTime time.Time) {
	// Calculate which bucket this connection should go in
	expireTick := expireTime.UnixNano() / int64(tw.tickSize)
	bucketIndex := int(expireTick % int64(tw.bucketCount))

	// Add to new bucket
	conn.bucketIndex = bucketIndex
	bucket := &tw.buckets[bucketIndex]

	// Add to head of doubly-linked list
	conn.next = bucket.head
	conn.prev = nil
	if bucket.head != nil {
		bucket.head.prev = conn
	} else {
		bucket.tail = conn
	}
	bucket.head = conn
}

// removeConnectionUnsafe removes a connection from its bucket (must hold lock)
func (tw *TimingWheel) removeConnectionUnsafe(conn *ConnectionInfo) {
	if conn.bucketIndex == -1 {
		return // Not in wheel
	}

	bucket := &tw.buckets[conn.bucketIndex]

	// Remove from doubly-linked list
	if conn.prev != nil {
		conn.prev.next = conn.next
	} else {
		bucket.head = conn.next
	}

	if conn.next != nil {
		conn.next.prev = conn.prev
	} else {
		bucket.tail = conn.prev
	}

	// Clear connection's wheel fields
	conn.bucketIndex = -1
	conn.next = nil
	conn.prev = nil
}

// removeConnection removes a connection from the wheel
func (tw *TimingWheel) removeConnection(conn *ConnectionInfo) {
	tw.mu.Lock()
	defer tw.mu.Unlock()
	tw.removeConnectionUnsafe(conn)
}

// tick advances the wheel and returns expired connections
func (tw *TimingWheel) tick() []*ConnectionInfo {
	tw.mu.Lock()
	defer tw.mu.Unlock()

	now := time.Now()
	currentTick := now.UnixNano() / int64(tw.tickSize)

	var expired []*ConnectionInfo

	// Process all ticks that have passed since last check
	for tw.currentTick < currentTick {
		tw.currentTick++
		bucketIndex := int(tw.currentTick % int64(tw.bucketCount))
		bucket := &tw.buckets[bucketIndex]

		// Collect all connections in this bucket that are actually expired
		conn := bucket.head
		for conn != nil {
			next := conn.next
			lastActivityNanos := atomic.LoadInt64(&conn.lastActivity)
			lastActivityTime := time.Unix(0, lastActivityNanos)

			// Check if connection is actually expired (double-check due to wheel granularity)
			if now.Sub(lastActivityTime) >= connectionTimeout {
				expired = append(expired, conn)
				tw.removeConnectionUnsafe(conn)
			} else {
				// Connection not expired yet, re-add to appropriate bucket
				expireTime := lastActivityTime.Add(connectionTimeout)
				tw.removeConnectionUnsafe(conn)
				tw.addConnectionUnsafe(conn, expireTime)
			}

			conn = next
		}
	}

	return expired
}

// NewShardedPoWStore creates a new sharded PoW challenge store
func NewShardedPoWStore() *ShardedPoWStore {
	store := &ShardedPoWStore{}
	for i := 0; i < numShards; i++ {
		store.shards[i].challenges = make(map[string]*PoWChallenge)
	}
	return store
}

// NewShardedIPRateLimit creates a new sharded IP rate limiting store
func NewShardedIPRateLimit() *ShardedIPRateLimit {
	store := &ShardedIPRateLimit{}
	for i := 0; i < numShards; i++ {
		store.shards[i].requests = make(map[string]*IPRequestCount)
	}
	return store
}

// getShard returns the shard for a given string key
func (sps *ShardedPoWStore) getShard(key string) *PoWShard {
	hash := 0
	for _, b := range []byte(key) {
		hash = hash*31 + int(b)
	}
	return &sps.shards[hash&shardMask]
}

// Store stores a challenge in the appropriate shard
func (sps *ShardedPoWStore) Store(key string, challenge *PoWChallenge) {
	shard := sps.getShard(key)
	shard.mu.Lock()
	shard.challenges[key] = challenge
	shard.mu.Unlock()
}

// Load loads a challenge from the appropriate shard
func (sps *ShardedPoWStore) Load(key string) (*PoWChallenge, bool) {
	shard := sps.getShard(key)
	shard.mu.RLock()
	challenge, ok := shard.challenges[key]
	shard.mu.RUnlock()
	return challenge, ok
}

// LoadAndDelete loads and deletes a challenge from the appropriate shard
func (sps *ShardedPoWStore) LoadAndDelete(key string) (*PoWChallenge, bool) {
	shard := sps.getShard(key)
	shard.mu.Lock()
	challenge, ok := shard.challenges[key]
	if ok {
		delete(shard.challenges, key)
	}
	shard.mu.Unlock()
	return challenge, ok
}

// CleanupExpired removes expired challenges from all shards
func (sps *ShardedPoWStore) CleanupExpired(expiry time.Duration) int {
	now := time.Now()
	totalCleaned := 0

	for i := 0; i < numShards; i++ {
		shard := &sps.shards[i]
		shard.mu.Lock()
		for key, challenge := range shard.challenges {
			if now.Sub(challenge.CreationTime) > expiry {
				delete(shard.challenges, key)
				totalCleaned++
			}
		}
		shard.mu.Unlock()
	}
	return totalCleaned
}

// Count returns the total number of challenges across all shards
func (sps *ShardedPoWStore) Count() int64 {
	total := int64(0)
	for i := 0; i < numShards; i++ {
		shard := &sps.shards[i]
		shard.mu.RLock()
		total += int64(len(shard.challenges))
		shard.mu.RUnlock()
	}
	return total
}

// getShard returns the shard for a given IP string
func (sirl *ShardedIPRateLimit) getShard(ip string) *IPRateLimitShard {
	hash := 0
	for _, b := range []byte(ip) {
		hash = hash*31 + int(b)
	}
	return &sirl.shards[hash&shardMask]
}

// CheckAndIncrement checks rate limit and increments counter if allowed
func (sirl *ShardedIPRateLimit) CheckAndIncrement(ip string, limit int, window time.Duration) bool {
	shard := sirl.getShard(ip)
	shard.mu.Lock()
	defer shard.mu.Unlock()

	now := time.Now()
	reqCount, exists := shard.requests[ip]

	if !exists {
		shard.requests[ip] = &IPRequestCount{
			Count:       1,
			WindowStart: now,
		}
		return true
	}

	// Reset window if expired
	if now.Sub(reqCount.WindowStart) > window {
		reqCount.Count = 1
		reqCount.WindowStart = now
		return true
	}

	// Check if within limit
	if reqCount.Count >= int64(limit) {
		return false
	}

	reqCount.Count++
	return true
}

// CleanupExpired removes expired IP rate limit entries
func (sirl *ShardedIPRateLimit) CleanupExpired(window time.Duration) int {
	now := time.Now()
	totalCleaned := 0

	for i := 0; i < numShards; i++ {
		shard := &sirl.shards[i]
		shard.mu.Lock()
		for ip, reqCount := range shard.requests {
			if now.Sub(reqCount.WindowStart) > window*2 { // Keep for 2x window for safety
				delete(shard.requests, ip)
				totalCleaned++
			}
		}
		shard.mu.Unlock()
	}
	return totalCleaned
}

// generatePublicSalt creates a cryptographically secure random string
func generatePublicSalt() (string, error) {
	bytes := make([]byte, 16) // 128 bits of entropy
	if _, err := rand.Read(bytes); err != nil {
		return "", err
	}
	return hex.EncodeToString(bytes), nil
}

// generatePrivateSalt creates a cryptographically secure private salt
func generatePrivateSalt() (string, error) {
	bytes := make([]byte, 32) // 256 bits of entropy
	if _, err := rand.Read(bytes); err != nil {
		return "", err
	}
	return hex.EncodeToString(bytes), nil
}

// createPoWChallenge creates a new PoW challenge with the given difficulty
func createPoWChallenge(difficulty int) (publicSalt, privateSalt, solution string, err error) {
	publicSalt, err = generatePublicSalt()
	if err != nil {
		return "", "", "", err
	}

	privateSalt, err = generatePrivateSalt()
	if err != nil {
		return "", "", "", err
	}

	// Generate solution by finding a nonce that produces the required number of leading zeros
	target := strings.Repeat("0", difficulty)
	nonce := int64(0)

	for {
		candidate := fmt.Sprintf("%s:%s:%d", publicSalt, privateSalt, nonce)
		hash := sha256.Sum256([]byte(candidate))
		hashHex := hex.EncodeToString(hash[:])

		if strings.HasPrefix(hashHex, target) {
			solution = fmt.Sprintf("%d", nonce)
			break
		}
		nonce++

		// Prevent infinite loops in case of extremely high difficulty
		if nonce > 1000000 {
			return "", "", "", fmt.Errorf("failed to generate challenge within reasonable time")
		}
	}

	return publicSalt, privateSalt, solution, nil
}

// verifyPoWSolution verifies a PoW solution against the challenge
func verifyPoWSolution(publicSalt, privateSalt, solution string, difficulty int) bool {
	// Parse the nonce from solution
	nonce, err := strconv.ParseInt(solution, 10, 64)
	if err != nil {
		return false
	}

	// Recreate the hash
	candidate := fmt.Sprintf("%s:%s:%d", publicSalt, privateSalt, nonce)
	hash := sha256.Sum256([]byte(candidate))
	hashHex := hex.EncodeToString(hash[:])

	// Check if it meets the difficulty requirement
	target := strings.Repeat("0", difficulty)
	return strings.HasPrefix(hashHex, target)
}

// hashString creates a SHA256 hash of the input string
func hashString(input string) string {
	hash := sha256.Sum256([]byte(input))
	return hex.EncodeToString(hash[:])
}

// secureCompare performs a constant-time comparison of two strings
func secureCompare(a, b string) bool {
	return subtle.ConstantTimeCompare([]byte(a), []byte(b)) == 1
}

// generateJWTToken creates a JWT token for the given subdomain
func generateJWTToken(subdomain, secretKey string, expiryHours int) (string, error) {
	claims := PoWClaims{
		Subdomain: subdomain,
		RegisteredClaims: jwt.RegisteredClaims{
			ExpiresAt: jwt.NewNumericDate(time.Now().Add(time.Duration(expiryHours) * time.Hour)),
			IssuedAt:  jwt.NewNumericDate(time.Now()),
			NotBefore: jwt.NewNumericDate(time.Now()),
			Issuer:    "pow-server",
			Subject:   subdomain,
		},
	}

	token := jwt.NewWithClaims(jwt.SigningMethodHS256, claims)
	return token.SignedString([]byte(secretKey))
}

// validateJWTToken validates a JWT token and returns the claims
func validateJWTToken(tokenString, secretKey string) (*PoWClaims, error) {
	token, err := jwt.ParseWithClaims(tokenString, &PoWClaims{}, func(token *jwt.Token) (interface{}, error) {
		// Validate the signing method
		if _, ok := token.Method.(*jwt.SigningMethodHMAC); !ok {
			return nil, fmt.Errorf("unexpected signing method: %v", token.Header["alg"])
		}
		return []byte(secretKey), nil
	})

	if err != nil {
		return nil, err
	}

	if claims, ok := token.Claims.(*PoWClaims); ok && token.Valid {
		return claims, nil
	}

	return nil, fmt.Errorf("invalid token")
}

// setJWTCookie sets a JWT token as an HTTP-only cookie
func setJWTCookie(w http.ResponseWriter, cookieName, token, subdomain string, expiryHours int) {
	cookie := &http.Cookie{
		Name:     cookieName,
		Value:    token,
		Path:     "/",
		Domain:   subdomain,
		MaxAge:   expiryHours * 3600, // Convert hours to seconds
		HttpOnly: true,
		Secure:   true, // Should be true in production with HTTPS
		SameSite: http.SameSiteStrictMode,
	}
	http.SetCookie(w, cookie)
}

// getJWTFromCookie extracts JWT token from cookie
func getJWTFromCookie(r *http.Request, cookieName string) (string, error) {
	cookie, err := r.Cookie(cookieName)
	if err != nil {
		return "", err
	}
	return cookie.Value, nil
}

// extractSubdomainFromHost extracts subdomain from host header
func extractSubdomainFromHost(host string) string {
	// Remove port if present
	if colonIndex := strings.Index(host, ":"); colonIndex != -1 {
		host = host[:colonIndex]
	}

	// Convert to lowercase for consistent comparison
	host = strings.ToLower(host)

	// Return the full host as subdomain for cookie scoping
	return host
}

// ChallengeRequest represents the structure for challenge requests
type ChallengeRequest struct {
	PublicSalt  string `json:"public_salt"`
	Difficulty  int    `json:"difficulty"`
	PrivateSalt string `json:"private_salt,omitempty"` // Only sent to server, not client
}

// ChallengeResponse represents the response structure for challenges
type ChallengeResponse struct {
	PublicSalt  string `json:"public_salt"`
	PrivateSalt string `json:"private_salt"`
	Difficulty  int    `json:"difficulty"`
	ExpiresAt   int64  `json:"expires_at"`
}

// VerifyRequest represents the structure for solution verification
type VerifyRequest struct {
	PublicSalt string `json:"public_salt"`
	Solution   string `json:"solution"`
}

// VerifyResponse represents the response structure for verification
type VerifyResponse struct {
	Success bool   `json:"success"`
	Error   string `json:"error,omitempty"`
	Token   string `json:"token,omitempty"`
}

// MetricsResponse represents the response structure for metrics
type MetricsResponse struct {
	Metrics PoWMetrics `json:"metrics"`
	Uptime  string     `json:"uptime"`
}

// addSecurityHeaders adds security headers to HTTP responses
func addSecurityHeaders(w http.ResponseWriter, config *Config) {
	if config.SecurityHeaders != nil {
		for key, value := range config.SecurityHeaders {
			w.Header().Set(key, value)
		}
	}

	// Always add these essential headers
	w.Header().Set("X-Content-Type-Options", "nosniff")
	w.Header().Set("X-Frame-Options", "DENY")
	w.Header().Set("X-XSS-Protection", "1; mode=block")
}

// getClientIP extracts the client IP from the request
func getClientIP(r *http.Request) string {
	// Check X-Forwarded-For header first (for reverse proxies)
	if xff := r.Header.Get("X-Forwarded-For"); xff != "" {
		// Take the first IP in the chain
		if commaIndex := strings.Index(xff, ","); commaIndex != -1 {
			return strings.TrimSpace(xff[:commaIndex])
		}
		return strings.TrimSpace(xff)
	}

	// Check X-Real-IP header
	if xri := r.Header.Get("X-Real-IP"); xri != "" {
		return strings.TrimSpace(xri)
	}

	// Fall back to RemoteAddr
	if colonIndex := strings.LastIndex(r.RemoteAddr, ":"); colonIndex != -1 {
		return r.RemoteAddr[:colonIndex]
	}
	return r.RemoteAddr
}

// handleChallenge handles GET /challenge requests
func (rs *rpsServer) handleChallenge(w http.ResponseWriter, r *http.Request) {
	addSecurityHeaders(w, rs.config)
	w.Header().Set("Content-Type", "application/json")

	// Rate limiting
	clientIP := getClientIP(r)
	if !rs.ipRateLimit.CheckAndIncrement(clientIP, rs.config.RateLimitPerMinute, time.Minute) {
		atomic.AddInt64(&rs.metrics.RateLimitedRequests, 1)
		w.WriteHeader(http.StatusTooManyRequests)
		json.NewEncoder(w).Encode(map[string]string{
			"error": "Rate limit exceeded. Please try again later.",
		})
		return
	}

	// Generate challenge
	publicSalt, privateSalt, solution, err := createPoWChallenge(rs.currentDifficulty)
	if err != nil {
		logger.Error("Failed to create PoW challenge", zap.Error(err))
		w.WriteHeader(http.StatusInternalServerError)
		json.NewEncoder(w).Encode(map[string]string{
			"error": "Failed to generate challenge",
		})
		return
	}

	// Store challenge
	challenge := &PoWChallenge{
		Solution:     solution,
		CreationTime: time.Now(),
		Difficulty:   rs.currentDifficulty,
	}

	// Use private salt as key for storage (not exposed to client)
	rs.powStore.Store(privateSalt, challenge)

	// Update metrics
	atomic.AddInt64(&rs.metrics.ChallengesIssued, 1)

	// Respond with challenge (without solution)
	response := ChallengeResponse{
		PublicSalt:  publicSalt,
		PrivateSalt: privateSalt,
		Difficulty:  rs.currentDifficulty,
		ExpiresAt:   time.Now().Add(time.Duration(rs.config.ChallengeExpiryMinutes) * time.Minute).Unix(),
	}

	json.NewEncoder(w).Encode(response)

	logger.Info("Challenge issued",
		zap.String("client_ip", clientIP),
		zap.String("public_salt", publicSalt),
		zap.Int("difficulty", rs.currentDifficulty))
}

// handleVerify handles POST /verify requests
func (rs *rpsServer) handleVerify(w http.ResponseWriter, r *http.Request) {
	addSecurityHeaders(w, rs.config)
	w.Header().Set("Content-Type", "application/json")

	if r.Method != http.MethodPost {
		w.WriteHeader(http.StatusMethodNotAllowed)
		json.NewEncoder(w).Encode(map[string]string{
			"error": "Method not allowed",
		})
		return
	}

	// Rate limiting
	clientIP := getClientIP(r)
	if !rs.ipRateLimit.CheckAndIncrement(clientIP, rs.config.RateLimitPerMinute, time.Minute) {
		atomic.AddInt64(&rs.metrics.RateLimitedRequests, 1)
		w.WriteHeader(http.StatusTooManyRequests)
		json.NewEncoder(w).Encode(map[string]string{
			"error": "Rate limit exceeded. Please try again later.",
		})
		return
	}

	// Parse request
	var req VerifyRequest
	if err := json.NewDecoder(r.Body).Decode(&req); err != nil {
		w.WriteHeader(http.StatusBadRequest)
		json.NewEncoder(w).Encode(map[string]string{
			"error": "Invalid request format",
		})
		return
	}

	// Load and verify challenge
	challenge, exists := rs.powStore.LoadAndDelete(req.PublicSalt)
	if !exists {
		atomic.AddInt64(&rs.metrics.ChallengesFailed, 1)
		w.WriteHeader(http.StatusBadRequest)
		json.NewEncoder(w).Encode(map[string]string{
			"error": "Challenge not found or expired",
		})
		return
	}

	// Check if challenge has expired
	if time.Since(challenge.CreationTime) > time.Duration(rs.config.ChallengeExpiryMinutes)*time.Minute {
		atomic.AddInt64(&rs.metrics.ChallengesFailed, 1)
		w.WriteHeader(http.StatusBadRequest)
		json.NewEncoder(w).Encode(map[string]string{
			"error": "Challenge expired",
		})
		return
	}

	// Verify solution
	if !secureCompare(challenge.Solution, req.Solution) {
		atomic.AddInt64(&rs.metrics.ChallengesFailed, 1)
		w.WriteHeader(http.StatusBadRequest)
		json.NewEncoder(w).Encode(map[string]string{
			"error": "Invalid solution",
		})
		return
	}

	// Generate JWT token
	subdomain := extractSubdomainFromHost(r.Host)
	token, err := generateJWTToken(subdomain, rs.config.JWTSecretKey, rs.config.JWTExpiryHours)
	if err != nil {
		logger.Error("Failed to generate JWT token", zap.Error(err))
		w.WriteHeader(http.StatusInternalServerError)
		json.NewEncoder(w).Encode(map[string]string{
			"error": "Failed to generate token",
		})
		return
	}

	// Set JWT cookie
	cookie := &http.Cookie{
		Name:     rs.config.JWTCookieName,
		Value:    token,
		Path:     "/",
		Domain:   subdomain,
		MaxAge:   rs.config.JWTExpiryHours * 3600,
		HttpOnly: true,
		Secure:   true, // Should be true in production with HTTPS
		SameSite: http.SameSiteStrictMode,
	}
	http.SetCookie(w, cookie)

	// Update metrics
	atomic.AddInt64(&rs.metrics.ChallengesVerified, 1)

	// Respond with success
	response := VerifyResponse{
		Success: true,
		Token:   token,
	}
	json.NewEncoder(w).Encode(response)

	logger.Info("Challenge verified successfully",
		zap.String("client_ip", clientIP),
		zap.String("subdomain", subdomain),
		zap.Int("difficulty", challenge.Difficulty))
}

// handleMetrics handles GET /metrics requests
func (rs *rpsServer) handleMetrics(w http.ResponseWriter, r *http.Request) {
	if !rs.config.MetricsEnabled {
		w.WriteHeader(http.StatusNotFound)
		return
	}

	addSecurityHeaders(w, rs.config)
	w.Header().Set("Content-Type", "application/json")

	// Update active challenges count
	rs.metrics.ActiveChallenges = rs.powStore.Count()
	rs.metrics.CurrentDifficulty = rs.currentDifficulty

	response := MetricsResponse{
		Metrics: rs.metrics,
		Uptime:  time.Since(rs.startTime).String(),
	}

	json.NewEncoder(w).Encode(response)
}

// loadConfig loads configuration from config.json
func loadConfig() (*Config, error) {
	data, err := os.ReadFile("config.json")
	if err != nil {
		return nil, fmt.Errorf("failed to read config.json: %w", err)
	}

	var config Config
	if err := json.Unmarshal(data, &config); err != nil {
		return nil, fmt.Errorf("failed to parse config.json: %w", err)
	}

	// Validate configuration
	if config.InitialDifficulty < 1 || config.InitialDifficulty > 10 {
		return nil, fmt.Errorf("initial_difficulty must be between 1 and 10")
	}
	if config.JWTSecretKey == "" {
		return nil, fmt.Errorf("jwt_secret_key cannot be empty")
	}
	if config.JWTCookieName == "" {
		config.JWTCookieName = "pow_session"
	}

	return &config, nil
}

// loadChallengeHTML loads the challenge.html file into memory
func loadChallengeHTML() ([]byte, error) {
	data, err := os.ReadFile("challenge.html")
	if err != nil {
		return nil, fmt.Errorf("failed to read challenge.html: %w", err)
	}
	return data, nil
}

// NewShardedConnTracker creates a new sharded connection tracker
func NewShardedConnTracker() *ShardedConnTracker {
	tracker := &ShardedConnTracker{}
	for i := 0; i < numShards; i++ {
		tracker.shards[i].conns = make(map[int]*ConnectionInfo)
	}
	return tracker
}

// getShard returns the shard for a given file descriptor
func (sct *ShardedConnTracker) getShard(fd int) *ConnShard {
	return &sct.shards[fd&shardMask]
}

// Store stores a connection info in the appropriate shard
func (sct *ShardedConnTracker) Store(fd int, connInfo *ConnectionInfo) {
	shard := sct.getShard(fd)
	shard.mu.Lock()
	shard.conns[fd] = connInfo
	shard.mu.Unlock()
}

// Load loads a connection info from the appropriate shard
func (sct *ShardedConnTracker) Load(fd int) (*ConnectionInfo, bool) {
	shard := sct.getShard(fd)
	shard.mu.RLock()
	connInfo, ok := shard.conns[fd]
	shard.mu.RUnlock()
	return connInfo, ok
}

// LoadAndDelete loads and deletes a connection info from the appropriate shard
func (sct *ShardedConnTracker) LoadAndDelete(fd int) (*ConnectionInfo, bool) {
	shard := sct.getShard(fd)
	shard.mu.Lock()
	connInfo, ok := shard.conns[fd]
	if ok {
		delete(shard.conns, fd)
	}
	shard.mu.Unlock()
	return connInfo, ok
}

// Range iterates over all connections in all shards
func (sct *ShardedConnTracker) Range(fn func(fd int, connInfo *ConnectionInfo) bool) {
	for i := 0; i < numShards; i++ {
		shard := &sct.shards[i]
		shard.mu.RLock()
		for fd, connInfo := range shard.conns {
			if !fn(fd, connInfo) {
				shard.mu.RUnlock()
				return
			}
		}
		shard.mu.RUnlock()
	}
}

// SubdomainStats holds statistics for a specific subdomain
type SubdomainStats struct {
	connections int64
	accepts     int64
	handled     int64
	requests    int64
	reading     int64
	// RPS tracking
	lastRequests int64
	currentRPS   int64
}

// Subdomain indices for array-based lookup (faster than map)
const (
	subdomainJS = iota
	subdomainUAM
	subdomainManaged
	subdomainDefault
	subdomainCount
)

// Subdomain names for display
var subdomainNames = [subdomainCount]string{
	subdomainJS:      "js.relayed.network",
	subdomainUAM:     "uam.relayed.network",
	subdomainManaged: "managed.relayed.network",
	subdomainDefault: "default/other",
}

type rpsServer struct {
	gnet.BuiltinEventEngine
	addr      string
	multicore bool
	eng       gnet.Engine
	// Global stats
	connections int64
	// Pre-allocated per-subdomain stats (no map lookups, no mutex needed)
	subdomainStats [subdomainCount]SubdomainStats
	// Connection tracking for timeouts - now using sharded map for better concurrency
	connTracker *ShardedConnTracker
	// Timing wheel for O(1) timeout management
	timingWheel *TimingWheel
	// UI state
	startTime    time.Time
	uiTicker     *time.Ticker
	lastUIUpdate time.Time
	uiCancel     context.CancelFunc
	// PGO profiling server
	pprofServer *http.Server

	// PoW system components
	config            *Config
	powStore          *ShardedPoWStore
	ipRateLimit       *ShardedIPRateLimit
	metrics           PoWMetrics
	currentDifficulty int
	challengeHTML     []byte

	// HTTP server for PoW endpoints
	httpServer *http.Server
}

// extractSubdomainIndex extracts subdomain index from host header value using optimized byte operations
func extractSubdomainIndex(hostValue []byte) int {
	// Remove port if present (find colon) - optimized with direct byte comparison
	for i, b := range hostValue {
		if b == colonChar {
			hostValue = hostValue[:i]
			break
		}
	}

	// Fast length-based pre-filtering before byte comparison
	hostLen := len(hostValue)
	switch hostLen {
	case len(jsSubdomain):
		if bytes.Equal(hostValue, jsSubdomain) {
			return subdomainJS
		}
	case len(uamSubdomain):
		if bytes.Equal(hostValue, uamSubdomain) {
			return subdomainUAM
		}
	case len(managedSubdomain):
		if bytes.Equal(hostValue, managedSubdomain) {
			return subdomainManaged
		}
	}

	// Default for unknown hosts
	return subdomainDefault
}

func (rs *rpsServer) OnBoot(eng gnet.Engine) (action gnet.Action) {
	rs.eng = eng
	rs.startTime = time.Now()
	rs.lastUIUpdate = time.Now()

	// Load configuration
	config, err := loadConfig()
	if err != nil {
		logger.Fatal("Failed to load configuration", zap.Error(err))
	}
	rs.config = config
	rs.currentDifficulty = config.InitialDifficulty

	// Load challenge HTML into memory
	challengeHTML, err := loadChallengeHTML()
	if err != nil {
		logger.Fatal("Failed to load challenge.html", zap.Error(err))
	}
	rs.challengeHTML = challengeHTML

	// Initialize PoW system components
	rs.powStore = NewShardedPoWStore()
	rs.ipRateLimit = NewShardedIPRateLimit()
	rs.metrics = PoWMetrics{
		CurrentDifficulty: rs.currentDifficulty,
	}

	// Initialize sharded connection tracker
	rs.connTracker = NewShardedConnTracker()

	// Initialize timing wheel for O(1) timeout management
	// Use 1-second buckets with enough buckets to cover 2x the timeout period
	wheelBuckets := int(connectionTimeout.Seconds()) * 2
	rs.timingWheel = NewTimingWheel(time.Second, wheelBuckets)

	// Start HTTP server for PoW endpoints
	mux := http.NewServeMux()
	mux.HandleFunc("/challenge", rs.handleChallenge)
	mux.HandleFunc("/verify", rs.handleVerify)
	mux.HandleFunc("/metrics", rs.handleMetrics)

	// Serve challenge.html for any other requests (will be handled by middleware)
	mux.HandleFunc("/", func(w http.ResponseWriter, r *http.Request) {
		addSecurityHeaders(w, rs.config)
		w.Header().Set("Content-Type", "text/html; charset=utf-8")
		w.Write(rs.challengeHTML)
	})

	rs.httpServer = &http.Server{
		Addr:    fmt.Sprintf(":%d", rs.config.Port),
		Handler: mux,
	}

	go func() {
		logger.Info("Starting HTTP server for PoW endpoints", zap.Int("port", rs.config.Port))
		if err := rs.httpServer.ListenAndServe(); err != nil && err != http.ErrServerClosed {
			logger.Error("HTTP server error", zap.Error(err))
		}
	}()

	// Start pprof server for PGO profiling on a separate port
	rs.pprofServer = &http.Server{
		Addr: ":6060",
	}
	go func() {
		logger.Info("Starting pprof server for PGO profiling", zap.String("addr", ":6060"))
		if err := rs.pprofServer.ListenAndServe(); err != nil && err != http.ErrServerClosed {
			logger.Error("pprof server error", zap.Error(err))
		}
	}()

	// Start UI update goroutine with context for graceful shutdown
	ctx, cancel := context.WithCancel(context.Background())
	rs.uiCancel = cancel
	go rs.runUI(ctx)

	logger.Info("PoW system initialized",
		zap.Int("initial_difficulty", rs.currentDifficulty),
		zap.Int("challenge_expiry_minutes", rs.config.ChallengeExpiryMinutes),
		zap.Int("rate_limit_per_minute", rs.config.RateLimitPerMinute),
		zap.Bool("dynamic_difficulty", rs.config.EnableDynamicDifficulty))

	// No need to initialize subdomainStats array - zero values are fine
	return
}

func (rs *rpsServer) OnOpen(c gnet.Conn) (out []byte, action gnet.Action) {
	atomic.AddInt64(&rs.connections, 1)

	// Track connection for timeout management using pool to reduce allocations
	now := time.Now()
	connInfo := connInfoPool.Get().(*ConnectionInfo)
	atomic.StoreInt64(&connInfo.lastActivity, now.UnixNano())
	connInfo.conn = c
	connInfo.subdomainIndex = -1 // -1 indicates unknown subdomain
	connInfo.bucketIndex = -1    // Initialize timing wheel fields
	connInfo.next = nil
	connInfo.prev = nil

	rs.connTracker.Store(c.Fd(), connInfo)

	// Add to timing wheel for efficient timeout tracking
	expireTime := now.Add(connectionTimeout)
	rs.timingWheel.addConnection(connInfo, expireTime)

	return
}

func (rs *rpsServer) OnClose(c gnet.Conn, err error) (action gnet.Action) {
	atomic.AddInt64(&rs.connections, -1)

	// Remove connection from tracker and return to pool
	if connInfo, ok := rs.connTracker.LoadAndDelete(c.Fd()); ok {
		// Remove from timing wheel
		rs.timingWheel.removeConnection(connInfo)

		// Decrement subdomain connection count if it was assigned
		if connInfo.subdomainIndex != -1 {
			atomic.AddInt64(&rs.subdomainStats[connInfo.subdomainIndex].connections, -1)
		}

		// Clear the connection info before returning to pool
		connInfo.conn = nil
		connInfo.bucketIndex = -1
		connInfo.next = nil
		connInfo.prev = nil
		connInfoPool.Put(connInfo)
	}

	return
}

// checkJWTFromRequest extracts and validates JWT from HTTP request data
func (rs *rpsServer) checkJWTFromRequest(data []byte, hostValue []byte) bool {
	// Look for Cookie header
	headerStart := 0
	maxHeaderEnd := len(data)
	if maxHeaderEnd > maxHeaderSize {
		maxHeaderEnd = maxHeaderSize
	}

	cookieHeaderPrefix := []byte("cookie:")

	for headerStart < maxHeaderEnd {
		headerEnd := bytes.IndexByte(data[headerStart:maxHeaderEnd], lfChar)
		if headerEnd < 0 {
			break
		}
		headerEnd += headerStart

		headerLine := data[headerStart:headerEnd]
		if len(headerLine) > 0 && headerLine[len(headerLine)-1] == crChar {
			headerLine = headerLine[:len(headerLine)-1]
		}

		// Check for Cookie header
		if len(headerLine) >= 7 && bytes.EqualFold(headerLine[:7], cookieHeaderPrefix) {
			// Extract cookie value
			cookieStart := 7
			for cookieStart < len(headerLine) && (headerLine[cookieStart] == spaceChar || headerLine[cookieStart] == tabChar) {
				cookieStart++
			}

			if cookieStart < len(headerLine) {
				cookieValue := string(headerLine[cookieStart:])

				// Parse cookies to find JWT cookie
				cookies := strings.Split(cookieValue, ";")
				for _, cookie := range cookies {
					cookie = strings.TrimSpace(cookie)
					if strings.HasPrefix(cookie, rs.config.JWTCookieName+"=") {
						tokenValue := cookie[len(rs.config.JWTCookieName)+1:]

						// Validate JWT token
						claims, err := validateJWTToken(tokenValue, rs.config.JWTSecretKey)
						if err != nil {
							return false
						}

						// Check if token is for the correct subdomain
						expectedSubdomain := extractSubdomainFromHost(string(hostValue))
						if claims.Subdomain != expectedSubdomain {
							return false
						}

						return true
					}
				}
			}
			break
		}

		headerStart = headerEnd + 1
	}

	return false
}

func (rs *rpsServer) OnTraffic(c gnet.Conn) (action gnet.Action) {
	// Update last activity time for timeout tracking
	var connInfo *ConnectionInfo
	if info, ok := rs.connTracker.Load(c.Fd()); ok {
		connInfo = info
		now := time.Now()
		atomic.StoreInt64(&connInfo.lastActivity, now.UnixNano())

		// Update timing wheel with new expiry time
		expireTime := now.Add(connectionTimeout)
		rs.timingWheel.addConnection(connInfo, expireTime)
	}

	// Peek(-1) returns all bytes in the ring-buffer without copying. This is a zero-copy operation.
	buf, err := c.Peek(-1)
	if err != nil {
		return gnet.Close
	}

	// Early size check to prevent processing oversized requests
	if len(buf) > maxRequestSize {
		return gnet.Close
	}

	// Find the end of the HTTP request, marked by a double CRLF.
	idx := bytes.Index(buf, doubleCRLF)
	if idx < 0 {
		// Incomplete request, wait for more data.
		return gnet.None
	}
	requestLen := idx + len(doubleCRLF)

	// Defer the discard operation. It will be executed on all return paths from this point forward,
	// ensuring the processed data is removed from the buffer before the connection is closed.
	defer func() {
		_, _ = c.Discard(requestLen)
	}()

	// Create a slice that views the request data from the buffer. This is also a zero-copy operation.
	data := buf[:requestLen]

	// Parse HTTP request to extract path and host - optimized parsing
	lineEnd := bytes.IndexByte(data, lfChar)
	if lineEnd < 0 {
		c.Write(blankResponse)
		return gnet.Close
	}
	requestLine := data[:lineEnd]
	if len(requestLine) > 0 && requestLine[len(requestLine)-1] == crChar {
		requestLine = requestLine[:len(requestLine)-1]
	}

	// Extract path from request line
	firstSpace := bytes.IndexByte(requestLine, spaceChar)
	if firstSpace < 0 {
		c.Write(blankResponse)
		return gnet.Close
	}
	pathAndQuery := requestLine[firstSpace+1:]
	secondSpace := bytes.IndexByte(pathAndQuery, spaceChar)
	var path []byte
	if secondSpace < 0 {
		path = pathAndQuery
	} else {
		path = pathAndQuery[:secondSpace]
	}

	// Extract Host header using optimized zero-copy byte operations
	subdomainIndex := subdomainDefault
	var hostValue []byte
	headerStart := lineEnd + 1

	// Limit header parsing to prevent DoS attacks
	maxHeaderEnd := headerStart + maxHeaderSize
	if maxHeaderEnd > len(data) {
		maxHeaderEnd = len(data)
	}

	for headerStart < maxHeaderEnd {
		headerEnd := bytes.IndexByte(data[headerStart:maxHeaderEnd], lfChar)
		if headerEnd < 0 {
			break
		}
		headerEnd += headerStart

		headerLine := data[headerStart:headerEnd]
		if len(headerLine) > 0 && headerLine[len(headerLine)-1] == crChar {
			headerLine = headerLine[:len(headerLine)-1]
		}

		// Optimized case-insensitive comparison for "host:" header
		if len(headerLine) >= 5 && bytes.EqualFold(headerLine[:5], hostHeader) {

			// Extract host value (skip "host:" and trim spaces) - optimized trimming
			hostStart := 5
			for hostStart < len(headerLine) && (headerLine[hostStart] == spaceChar || headerLine[hostStart] == tabChar) {
				hostStart++
			}
			if hostStart < len(headerLine) {
				hostValue = headerLine[hostStart:]
				subdomainIndex = extractSubdomainIndex(hostValue)
			}
			break
		}

		headerStart = headerEnd + 1
	}

	// PoW Middleware: Check for valid JWT token for protected paths
	// Skip PoW check for PoW API endpoints
	pathStr := string(path)
	isPoWEndpoint := pathStr == "/challenge" || pathStr == "/verify" || pathStr == "/metrics"

	if !isPoWEndpoint {
		// Check JWT token
		hasValidJWT := rs.checkJWTFromRequest(data, hostValue)

		if !hasValidJWT {
			// Serve challenge page with redirect parameter
			redirectURL := pathStr
			if len(pathAndQuery) > len(path) {
				redirectURL = string(pathAndQuery)
			}

			// Build challenge page response with redirect parameter
			challengePageWithRedirect := bytes.Replace(rs.challengeHTML,
				[]byte("const redirectUrl = urlParams.get('redirect') || '/';"),
				[]byte(fmt.Sprintf("const redirectUrl = urlParams.get('redirect') || '%s';", redirectURL)), 1)

			header := statusHeaderPool.Get().([]byte)
			header = header[:0]
			header = append(header, "HTTP/1.1 200 OK\r\n"...)
			header = append(header, "Content-Type: text/html; charset=utf-8\r\n"...)

			// Add security headers
			if rs.config.SecurityHeaders != nil {
				for key, value := range rs.config.SecurityHeaders {
					header = append(header, key...)
					header = append(header, ": "...)
					header = append(header, value...)
					header = append(header, "\r\n"...)
				}
			}

			header = append(header, "Content-Length: "...)
			header = strconv.AppendInt(header, int64(len(challengePageWithRedirect)), 10)
			header = append(header, "\r\n\r\n"...)

			c.Writev([][]byte{header, challengePageWithRedirect})
			statusHeaderPool.Put(header)
			return gnet.Close
		}
	}

	// Update per-subdomain statistics using direct array access (no locks, no allocations)
	subStats := &rs.subdomainStats[subdomainIndex]
	atomic.AddInt64(&subStats.requests, 1)
	atomic.AddInt64(&subStats.reading, 1)
	defer atomic.AddInt64(&subStats.reading, -1)

	// Associate connection with subdomain if not already done
	if connInfo != nil && connInfo.subdomainIndex == -1 {
		connInfo.subdomainIndex = subdomainIndex
		atomic.AddInt64(&subStats.connections, 1)
	}

	// For nginx status compatibility, let's assume accepts/handled are per-request
	atomic.AddInt64(&subStats.accepts, 1)
	atomic.AddInt64(&subStats.handled, 1)

	if bytes.Equal(path, pathNginxStatus) || bytes.Equal(path, pathStatus) {
		// Use subdomain-specific statistics
		activeConnections := atomic.LoadInt64(&rs.subdomainStats[subdomainIndex].connections)
		reading := atomic.LoadInt64(&subStats.reading)
		waiting := activeConnections - reading
		if waiting < 0 {
			waiting = 0
		}

		subdomainAccepts := atomic.LoadInt64(&subStats.accepts)
		subdomainHandled := atomic.LoadInt64(&subStats.handled)
		subdomainRequests := atomic.LoadInt64(&subStats.requests)

		// build body in exact nginx status format
		body := statusBodyPool.Get().([]byte)
		body = body[:0]
		body = append(body, "Active connections: "...)
		body = strconv.AppendInt(body, activeConnections, 10)
		body = append(body, " \nserver accepts handled requests\n "...)
		body = strconv.AppendInt(body, subdomainAccepts, 10)
		body = append(body, ' ')
		body = strconv.AppendInt(body, subdomainHandled, 10)
		body = append(body, ' ')
		body = strconv.AppendInt(body, subdomainRequests, 10)
		body = append(body, " \nReading: "...)
		body = strconv.AppendInt(body, reading, 10)
		body = append(body, " Writing: "...)
		body = strconv.AppendInt(body, 0, 10) // gnet doesn't expose writing stats directly
		body = append(body, " Waiting: "...)
		body = strconv.AppendInt(body, waiting, 10)
		body = append(body, " \n"...)

		// build header with security headers
		header := statusHeaderPool.Get().([]byte)
		header = header[:0]
		header = append(header, "HTTP/1.1 200 OK\r\n"...)
		header = append(header, "Content-Type: text/plain\r\n"...)

		// Add security headers
		if rs.config.SecurityHeaders != nil {
			for key, value := range rs.config.SecurityHeaders {
				header = append(header, key...)
				header = append(header, ": "...)
				header = append(header, value...)
				header = append(header, "\r\n"...)
			}
		}

		header = append(header, "Content-Length: "...)
		header = strconv.AppendInt(header, int64(len(body)), 10)
		header = append(header, "\r\n\r\n"...)

		c.Writev([][]byte{header, body})
		statusHeaderPool.Put(header)
		statusBodyPool.Put(body)
	} else {
		// Serve blank response with security headers
		header := statusHeaderPool.Get().([]byte)
		header = header[:0]
		header = append(header, "HTTP/1.1 200 OK\r\n"...)

		// Add security headers
		if rs.config.SecurityHeaders != nil {
			for key, value := range rs.config.SecurityHeaders {
				header = append(header, key...)
				header = append(header, ": "...)
				header = append(header, value...)
				header = append(header, "\r\n"...)
			}
		}

		header = append(header, "Content-Length: 0\r\n\r\n"...)
		c.Write(header)
		statusHeaderPool.Put(header)
	}

	return gnet.Close
}

// runUI runs the text-based UI in a separate goroutine
func (rs *rpsServer) runUI(ctx context.Context) {
	ticker := time.NewTicker(uiUpdateInterval)
	defer ticker.Stop()

	// Clear screen and hide cursor
	fmt.Print("\033[2J\033[?25l")

	for {
		select {
		case <-ticker.C:
			rs.updateUI()
		case <-ctx.Done():
			return
		}
	}
}

// updateUI refreshes the console display using optimized buffered output
func (rs *rpsServer) updateUI() {
	now := time.Now()
	uptime := now.Sub(rs.startTime)

	// Calculate RPS for each subdomain
	for i := 0; i < subdomainCount; i++ {
		currentRequests := atomic.LoadInt64(&rs.subdomainStats[i].requests)
		lastRequests := atomic.LoadInt64(&rs.subdomainStats[i].lastRequests)
		rps := currentRequests - lastRequests
		atomic.StoreInt64(&rs.subdomainStats[i].currentRPS, rps)
		atomic.StoreInt64(&rs.subdomainStats[i].lastRequests, currentRequests)
	}

	// Use strings.Builder for efficient buffered output (optimization from plan)
	var builder strings.Builder
	builder.Grow(2048) // Pre-allocate buffer to reduce reallocations

	// Move cursor to top-left and clear screen
	builder.WriteString("\033[H\033[2J")

	// Header
	builder.WriteString("╔══════════════════════════════════════════════════════════════════════════════╗\n")
	builder.WriteString("║                            RPS Server Monitor                               ║\n")
	builder.WriteString("╠══════════════════════════════════════════════════════════════════════════════╣\n")
	fmt.Fprintf(&builder, "║ Uptime: %-20s │ Total Active Connections: %-10d │ Port: 9000    ║\n",
		rs.formatDuration(uptime), atomic.LoadInt64(&rs.connections))
	builder.WriteString("╠══════════════════════════════════════════════════════════════════════════════╣\n")
	builder.WriteString("║                              Subdomain Statistics                           ║\n")
	builder.WriteString("╠═══════════════════════════════╤═══════╤═══════════╤═══════╤═══════╤═══════════╣\n")
	builder.WriteString("║ Subdomain                     │  RPS  │ Requests  │ Active│ Read  │ Handled   ║\n")
	builder.WriteString("╠═══════════════════════════════╪═══════╪═══════════╪═══════╪═══════╪═══════════╣\n")

	// Subdomain stats
	for i := 0; i < subdomainCount; i++ {
		stats := &rs.subdomainStats[i]
		name := subdomainNames[i]
		if len(name) > 29 {
			name = name[:26] + "..."
		}

		rps := atomic.LoadInt64(&stats.currentRPS)
		total := atomic.LoadInt64(&stats.requests)
		connections := atomic.LoadInt64(&stats.connections)
		reading := atomic.LoadInt64(&stats.reading)
		handled := atomic.LoadInt64(&stats.handled)

		fmt.Fprintf(&builder, "║ %-29s │ %5d │ %9d │ %5d │ %5d │ %9d ║\n",
			name, rps, total, connections, reading, handled)
	}

	builder.WriteString("╚═══════════════════════════════╧═══════╧═══════════╧═══════╧═══════╧═══════════╝\n")
	builder.WriteString("\nPress Ctrl+C to stop the server\n")
	fmt.Fprintf(&builder, "Last updated: %s\n", now.Format("15:04:05"))

	// Single write operation to console (optimization from plan)
	fmt.Print(builder.String())
}

// formatDuration formats a duration in a human-readable way
func (rs *rpsServer) formatDuration(d time.Duration) string {
	if d < time.Minute {
		return fmt.Sprintf("%.0fs", d.Seconds())
	} else if d < time.Hour {
		return fmt.Sprintf("%.0fm %.0fs", d.Minutes(), math.Mod(d.Seconds(), 60))
	} else {
		hours := int(d.Hours())
		minutes := int(d.Minutes()) - hours*60
		return fmt.Sprintf("%dh %dm", hours, minutes)
	}
}

// OnTick implements O(1) timeout checking using timing wheel and PoW cleanup
func (rs *rpsServer) OnTick() (delay time.Duration, action gnet.Action) {
	// Use timing wheel to get expired connections in O(1) time
	expiredConnections := rs.timingWheel.tick()

	// Close timed-out connections with conditional logging for performance
	if len(expiredConnections) > 0 {
		// Only log if significant number of connections are being closed (reduces log spam)
		if len(expiredConnections) >= 10 {
			logger.Info("Closing timed-out connections",
				zap.Int("count", len(expiredConnections)),
				zap.Duration("timeout", connectionTimeout))
		}
		for _, connInfo := range expiredConnections {
			connInfo.conn.Close() // This will trigger OnClose which removes from tracker
		}
	}

	// PoW system cleanup
	if rs.powStore != nil && rs.ipRateLimit != nil {
		// Clean up expired challenges
		challengeExpiry := time.Duration(rs.config.ChallengeExpiryMinutes) * time.Minute
		expiredChallenges := rs.powStore.CleanupExpired(challengeExpiry)

		// Clean up expired IP rate limit entries
		expiredIPs := rs.ipRateLimit.CleanupExpired(time.Minute)

		// Log cleanup if significant
		if expiredChallenges > 0 || expiredIPs > 0 {
			logger.Debug("PoW cleanup completed",
				zap.Int("expired_challenges", expiredChallenges),
				zap.Int("expired_ips", expiredIPs))
		}

		// Dynamic difficulty adjustment (if enabled)
		if rs.config.EnableDynamicDifficulty {
			rs.adjustDifficulty()
		}
	}

	// Return cleanup interval for next tick
	return cleanupInterval, gnet.None
}

// adjustDifficulty dynamically adjusts PoW difficulty based on system performance
func (rs *rpsServer) adjustDifficulty() {
	// This is a simple implementation - can be enhanced with more sophisticated algorithms
	currentChallenges := rs.powStore.Count()

	// If too many active challenges, increase difficulty
	if currentChallenges > 1000 && rs.currentDifficulty < rs.config.MaxDifficulty {
		rs.currentDifficulty++
		rs.metrics.CurrentDifficulty = rs.currentDifficulty
		logger.Info("Increased PoW difficulty",
			zap.Int("new_difficulty", rs.currentDifficulty),
			zap.Int64("active_challenges", currentChallenges))
	} else if currentChallenges < 100 && rs.currentDifficulty > rs.config.MinDifficulty {
		// If very few active challenges, decrease difficulty
		rs.currentDifficulty--
		rs.metrics.CurrentDifficulty = rs.currentDifficulty
		logger.Info("Decreased PoW difficulty",
			zap.Int("new_difficulty", rs.currentDifficulty),
			zap.Int64("active_challenges", currentChallenges))
	}
}

// Shutdown gracefully stops the UI goroutine, HTTP server, and pprof server
func (rs *rpsServer) Shutdown() {
	if rs.uiCancel != nil {
		rs.uiCancel()
	}
	if rs.httpServer != nil {
		ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
		defer cancel()
		rs.httpServer.Shutdown(ctx)
	}
	if rs.pprofServer != nil {
		ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
		defer cancel()
		rs.pprofServer.Shutdown(ctx)
	}
}

// initLogger initializes a high-performance structured logger
func initLogger() {
	// Create a high-performance production logger configuration
	config := zap.NewProductionConfig()

	// Configure for high throughput with async logging
	config.Level = zap.NewAtomicLevelAt(zap.InfoLevel)
	config.Sampling = &zap.SamplingConfig{
		Initial:    100, // Log first 100 messages per second
		Thereafter: 100, // Then log every 100th message
	}

	// Use JSON encoding for structured logs
	config.Encoding = "json"

	// Configure encoder for performance
	config.EncoderConfig = zapcore.EncoderConfig{
		TimeKey:        "ts",
		LevelKey:       "level",
		NameKey:        "logger",
		CallerKey:      "caller",
		MessageKey:     "msg",
		StacktraceKey:  "stacktrace",
		LineEnding:     zapcore.DefaultLineEnding,
		EncodeLevel:    zapcore.LowercaseLevelEncoder,
		EncodeTime:     zapcore.EpochTimeEncoder, // Use epoch time for performance
		EncodeDuration: zapcore.SecondsDurationEncoder,
		EncodeCaller:   zapcore.ShortCallerEncoder,
	}

	var err error
	logger, err = config.Build(
		zap.AddCaller(),
		zap.AddStacktrace(zap.ErrorLevel), // Only add stacktrace for errors
	)
	if err != nil {
		log.Fatalf("failed to initialize logger: %v", err)
	}
}

func main() {
	// Initialize high-performance logger first
	initLogger()
	defer logger.Sync() // Flush any buffered log entries

	rs := &rpsServer{
		addr:      "tcp://:9000",
		multicore: true,
	}

	// Setup graceful shutdown to restore cursor and stop UI goroutine
	defer func() {
		rs.Shutdown()       // Stop UI goroutine gracefully
		fmt.Print("[?25h") // Show cursor
		if r := recover(); r != nil {
			logger.Error("Server panic", zap.Any("panic", r), zap.Stack("stack"))
		}
	}()

	logger.Info("Starting RPS server", zap.String("address", rs.addr))
	logger.Info("Connection timeout", zap.Duration("timeout", connectionTimeout))
	logger.Info("Cleanup interval", zap.Duration("interval", cleanupInterval))
	logger.Info("Multicore mode", zap.Bool("enabled", rs.multicore))
	logger.Info("Connection tracker shards", zap.Int("count", numShards))
	logger.Info("UI will start after server boot...")

	// Performance optimizations for gnet
	options := []gnet.Option{
		gnet.WithMulticore(rs.multicore),
		gnet.WithReusePort(true),             // Enable SO_REUSEPORT for better load balancing
		gnet.WithTCPKeepAlive(time.Minute),   // Enable TCP keepalive
		gnet.WithTCPNoDelay(gnet.TCPNoDelay), // Disable Nagle's algorithm for lower latency
		gnet.WithSocketRecvBuffer(64 * 1024), // Increase receive buffer
		gnet.WithSocketSendBuffer(64 * 1024), // Increase send buffer
	}

	if err := gnet.Run(rs, rs.addr, options...); err != nil {
		logger.Fatal("gnet server failed to run", zap.Error(err))
	}
}
