# Plan to Implement Proof of Work (PoW) - Enterprise Grade

This plan outlines the steps to integrate a robust, secure, and scalable Proof of Work system into the existing Go server. This system will act as a middleware, protecting all paths on all subdomains for a single-node deployment.

## 1. Project Dependencies

- **Go Modules:**
  - `crypto/sha256`, `crypto/rand`, `crypto/subtle`
  - `encoding/hex`, `encoding/json`
  - `math/big`
  - `os` (to read files)
  - `net/http` (for cookie handling)
  - `github.com/golang-jwt/jwt/v4` (for post-verification tokens)
  - `github.com/prometheus/client_golang/prometheus` (for metrics)
  - `go.uber.org/zap` (for structured logging)


## 2. Configuration and Data Structures

- **Configuration (`config.json`):**
  - `port`, `initial_difficulty`, `challenge_expiry_minutes`, `enable_dynamic_difficulty`, `rate_limit_per_minute`, `jwt_secret_key`, `jwt_cookie_name`.

- **Data Structures:**
  - `PoWChallenge` struct: `{ Solution string, CreationTime time.Time }`.
  - `shardedPoWStore`: A thread-safe, sharded in-memory map for challenges (`publicSalt` -> `PoWChallenge`).
  - `ipRequestCounts`: A thread-safe, in-memory map for rate limiting.

## 3. Core PoW Logic

- **Salt and Challenge Generation:**
  - `generatePublicSalt()`: Creates a cryptographically secure random string.
  - When a challenge is requested, generate salts and hashes, then store the solution in the `shardedPoWStore`.

## 4. Client-Side Implementation (`challenge.html`)

- **High-Performance Hashing with WebAssembly (WASM):**
  - For maximum client-side performance, the core hashing logic (the brute-force loop) will be written in a high-performance language like Rust or C++, compiled to WASM, and loaded by the JavaScript worker. This significantly speeds up solving the challenge compared to pure JS, reducing user wait time.

- **JavaScript Logic (with Web Worker & WASM):**
  - **Challenge Trigger:** The user is presented with this page automatically when trying to access any protected resource without a valid session.
  - **Solve and Verify:** The script fetches a challenge from `GET /challenge`, solves it, and sends the result to `POST /verify`.
  - **Redirection:** On successful verification, the server will set a JWT cookie. The script will then redirect the user back to their originally requested URL (which can be passed as a query parameter like `?redirect=/nginx_status`).

## 5. PoW Middleware & API Endpoints

- **Refactor `OnTraffic` into a Middleware-based Router:**
  - `OnTraffic` will act as a middleware that protects all routes/paths for all subdomains.
  - The middleware will check for a valid JWT on every incoming request.

- **PoW Middleware Flow:**
  1. For any request to any subdomain/path (e.g., `js.relayed.network/some/path`):
  2. Check for a valid JWT in an HTTP-only cookie. The cookie will be scoped to the specific subdomain the user is visiting.
  3. **If JWT is valid:** The request is passed through to the original application logic (e.g., serving `/nginx_status` or the blank response).
  4. **If JWT is invalid/missing:**
     - The server intercepts the request and serves the `challenge.html` page.
     - The client-side script solves the challenge, gets a new JWT set in a cookie via the `/verify` endpoint, and then redirects the user back to their original destination.

- **API Endpoints (Excluded from Middleware):**
  - The middleware must have exceptions for the PoW API endpoints themselves.
  - `GET /challenge`: Issues a new PoW challenge. Rate-limited.
  - `POST /verify`: Verifies the solution, and on success, sets the JWT cookie for the client's browser, scoped to the subdomain from the Host header.
  - `GET /metrics`: Exposes performance metrics.

- **Security Hardening:**
  - **HTTPS:** The server must be run behind a reverse proxy (like Nginx) that terminates SSL/TLS.
  - **Security Headers:** All responses should include `Content-Security-Policy` (CSP), `X-Content-Type-Options: nosniff`, etc.
  - **JWT Cookie:** The JWT should be stored in a secure, HTTP-only cookie with the `Secure` and `SameSite=Strict` flags.

## 6. System Maintenance & Monitoring

- **Challenge Cleanup:**
  - In `OnTick`, periodically iterate through the in-memory `shardedPoWStore` and `ipRequestCounts` to purge expired challenges and old IP records.

- **Dynamic Difficulty Adjustment:** In `OnTick`, monitor key performance indicators (KPIs) from the `/metrics` endpoint. Adjust `currentDifficulty` based on server load and average challenge solve time.

- **Advanced Logging & Metrics:**
  - Use `zap` for structured logging of all key events.
  - Expose Prometheus metrics for challenges issued, verified, solve times, and active difficulty.

## 7. Integration with `main.go`

- **Modify `rpsServer` struct:** Add the `shardedPoWStore`, `ipRequestCounts`, and router.
- **Modify `OnBoot`:** Load config, initialize data structures, and read `challenge.html` into memory.
- **Modify `OnTraffic`:** Implement the new middleware logic. It will parse the request, check for the JWT, and then either serve the challenge page or pass the request to the original handlers.
- **Modify `OnTick`:** Add calls to the cleanup and dynamic difficulty functions for the in-memory stores.
