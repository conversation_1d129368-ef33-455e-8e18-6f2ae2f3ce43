<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Proof of Work Challenge</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            margin: 0;
            padding: 0;
            min-height: 100vh;
            display: flex;
            justify-content: center;
            align-items: center;
        }
        .container {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 15px;
            padding: 40px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            text-align: center;
            max-width: 500px;
            width: 90%;
        }
        .logo {
            font-size: 48px;
            margin-bottom: 20px;
        }
        h1 {
            color: #333;
            margin-bottom: 20px;
            font-size: 28px;
        }
        .description {
            color: #666;
            margin-bottom: 30px;
            line-height: 1.6;
        }
        .progress-container {
            background: #f0f0f0;
            border-radius: 25px;
            padding: 4px;
            margin: 20px 0;
        }
        .progress-bar {
            background: linear-gradient(90deg, #667eea, #764ba2);
            height: 20px;
            border-radius: 20px;
            width: 0%;
            transition: width 0.3s ease;
        }
        .status {
            margin: 20px 0;
            font-weight: bold;
            color: #333;
        }
        .stats {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin: 20px 0;
        }
        .stat-item {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 10px;
            border-left: 4px solid #667eea;
        }
        .stat-label {
            font-size: 12px;
            color: #666;
            text-transform: uppercase;
            margin-bottom: 5px;
        }
        .stat-value {
            font-size: 18px;
            font-weight: bold;
            color: #333;
        }
        .spinner {
            border: 3px solid #f3f3f3;
            border-top: 3px solid #667eea;
            border-radius: 50%;
            width: 30px;
            height: 30px;
            animation: spin 1s linear infinite;
            margin: 0 auto 20px;
        }
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        .error {
            color: #e74c3c;
            background: #fdf2f2;
            padding: 15px;
            border-radius: 10px;
            margin: 20px 0;
            border-left: 4px solid #e74c3c;
        }
        .success {
            color: #27ae60;
            background: #f2fdf5;
            padding: 15px;
            border-radius: 10px;
            margin: 20px 0;
            border-left: 4px solid #27ae60;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="logo">🔐</div>
        <h1>Security Verification</h1>
        <div class="description">
            Please wait while we verify your request. This process helps protect against automated attacks and ensures the security of our services.
        </div>
        
        <div class="spinner" id="spinner"></div>
        
        <div class="progress-container">
            <div class="progress-bar" id="progressBar"></div>
        </div>
        
        <div class="status" id="status">Initializing challenge...</div>
        
        <div class="stats">
            <div class="stat-item">
                <div class="stat-label">Difficulty</div>
                <div class="stat-value" id="difficulty">-</div>
            </div>
            <div class="stat-item">
                <div class="stat-label">Attempts</div>
                <div class="stat-value" id="attempts">0</div>
            </div>
            <div class="stat-item">
                <div class="stat-label">Hash Rate</div>
                <div class="stat-value" id="hashRate">0 H/s</div>
            </div>
            <div class="stat-item">
                <div class="stat-label">Elapsed</div>
                <div class="stat-value" id="elapsed">0s</div>
            </div>
        </div>
        
        <div id="errorMessage" class="error" style="display: none;"></div>
        <div id="successMessage" class="success" style="display: none;"></div>
    </div>

    <script>
        class PoWSolver {
            constructor() {
                this.worker = null;
                this.startTime = null;
                this.attempts = 0;
                this.hashRate = 0;
                this.updateInterval = null;
            }

            async init() {
                try {
                    // Try to use Web Worker for better performance
                    this.worker = new Worker(URL.createObjectURL(new Blob([this.getWorkerCode()], { type: 'application/javascript' })));
                    this.worker.onmessage = this.handleWorkerMessage.bind(this);
                    this.worker.onerror = this.handleWorkerError.bind(this);
                    
                    await this.fetchAndSolveChallenge();
                } catch (error) {
                    this.showError('Failed to initialize: ' + error.message);
                }
            }

            getWorkerCode() {
                return `
                    // SHA-256 implementation for Web Worker
                    async function sha256(message) {
                        const msgBuffer = new TextEncoder().encode(message);
                        const hashBuffer = await crypto.subtle.digest('SHA-256', msgBuffer);
                        const hashArray = Array.from(new Uint8Array(hashBuffer));
                        return hashArray.map(b => b.toString(16).padStart(2, '0')).join('');
                    }

                    self.onmessage = async function(e) {
                        const { publicSalt, privateSalt, difficulty, startNonce = 0, batchSize = 10000 } = e.data;
                        const target = '0'.repeat(difficulty);
                        let nonce = startNonce;
                        let attempts = 0;
                        const startTime = Date.now();

                        try {
                            while (attempts < batchSize) {
                                const candidate = publicSalt + ':' + privateSalt + ':' + nonce;
                                const hash = await sha256(candidate);
                                
                                attempts++;
                                
                                if (hash.startsWith(target)) {
                                    self.postMessage({
                                        type: 'solution',
                                        nonce: nonce,
                                        attempts: attempts,
                                        timeElapsed: Date.now() - startTime
                                    });
                                    return;
                                }
                                
                                nonce++;
                                
                                // Report progress every 1000 attempts
                                if (attempts % 1000 === 0) {
                                    const elapsed = (Date.now() - startTime) / 1000;
                                    const hashRate = attempts / elapsed;
                                    self.postMessage({
                                        type: 'progress',
                                        attempts: attempts,
                                        hashRate: Math.round(hashRate),
                                        nonce: nonce
                                    });
                                }
                            }
                            
                            // Batch completed without solution
                            self.postMessage({
                                type: 'batchComplete',
                                nextNonce: nonce,
                                attempts: attempts
                            });
                        } catch (error) {
                            self.postMessage({
                                type: 'error',
                                error: error.message
                            });
                        }
                    };
                `;
            }

            async fetchAndSolveChallenge() {
                try {
                    this.updateStatus('Fetching challenge...');
                    
                    const response = await fetch('/challenge');
                    if (!response.ok) {
                        throw new Error('Failed to fetch challenge: ' + response.statusText);
                    }
                    
                    const challenge = await response.json();
                    this.updateDifficulty(challenge.difficulty);
                    
                    this.updateStatus('Solving challenge...');
                    this.startTime = Date.now();
                    this.attempts = 0;
                    
                    // Start progress updates
                    this.updateInterval = setInterval(() => this.updateElapsed(), 1000);
                    
                    await this.solveChallenge(challenge);
                } catch (error) {
                    this.showError('Challenge failed: ' + error.message);
                }
            }

            async solveChallenge(challenge) {
                return new Promise((resolve, reject) => {
                    let currentNonce = 0;
                    const batchSize = 50000; // Larger batch size for better performance
                    
                    const solveBatch = () => {
                        this.worker.postMessage({
                            publicSalt: challenge.public_salt,
                            privateSalt: challenge.private_salt,
                            difficulty: challenge.difficulty,
                            startNonce: currentNonce,
                            batchSize: batchSize
                        });
                    };
                    
                    this.worker.onmessage = async (e) => {
                        const { type, nonce, attempts, hashRate, nextNonce, error } = e.data;
                        
                        switch (type) {
                            case 'solution':
                                clearInterval(this.updateInterval);
                                this.updateStatus('Verifying solution...');
                                await this.submitSolution(challenge.public_salt, nonce.toString());
                                resolve();
                                break;
                                
                            case 'progress':
                                this.attempts += attempts;
                                this.hashRate = hashRate;
                                this.updateProgress();
                                break;
                                
                            case 'batchComplete':
                                this.attempts += attempts;
                                currentNonce = nextNonce;
                                this.updateProgress();
                                // Continue with next batch
                                setTimeout(solveBatch, 10); // Small delay to prevent blocking
                                break;
                                
                            case 'error':
                                clearInterval(this.updateInterval);
                                reject(new Error(error));
                                break;
                        }
                    };
                    
                    // Start solving
                    solveBatch();
                });
            }

            async submitSolution(publicSalt, solution) {
                try {
                    const response = await fetch('/verify', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                        },
                        body: JSON.stringify({
                            public_salt: publicSalt,
                            solution: solution
                        })
                    });
                    
                    if (response.ok) {
                        this.showSuccess('Challenge completed successfully! Redirecting...');
                        
                        // Get redirect URL from query parameters
                        const urlParams = new URLSearchParams(window.location.search);
                        const redirectUrl = urlParams.get('redirect') || '/';
                        
                        // Redirect after a short delay
                        setTimeout(() => {
                            window.location.href = redirectUrl;
                        }, 2000);
                    } else {
                        const errorData = await response.json();
                        throw new Error(errorData.error || 'Verification failed');
                    }
                } catch (error) {
                    this.showError('Solution verification failed: ' + error.message);
                }
            }

            handleWorkerMessage(e) {
                // Handled in solveChallenge method
            }

            handleWorkerError(error) {
                this.showError('Worker error: ' + error.message);
            }

            updateStatus(message) {
                document.getElementById('status').textContent = message;
            }

            updateDifficulty(difficulty) {
                document.getElementById('difficulty').textContent = difficulty;
            }

            updateProgress() {
                document.getElementById('attempts').textContent = this.attempts.toLocaleString();
                document.getElementById('hashRate').textContent = this.hashRate.toLocaleString() + ' H/s';
                
                // Update progress bar based on estimated completion (very rough estimate)
                const estimatedTotal = Math.pow(16, parseInt(document.getElementById('difficulty').textContent) || 4);
                const progress = Math.min((this.attempts / estimatedTotal) * 100, 95); // Cap at 95% until complete
                document.getElementById('progressBar').style.width = progress + '%';
            }

            updateElapsed() {
                if (this.startTime) {
                    const elapsed = Math.floor((Date.now() - this.startTime) / 1000);
                    document.getElementById('elapsed').textContent = elapsed + 's';
                }
            }

            showError(message) {
                document.getElementById('spinner').style.display = 'none';
                document.getElementById('errorMessage').textContent = message;
                document.getElementById('errorMessage').style.display = 'block';
                document.getElementById('successMessage').style.display = 'none';
            }

            showSuccess(message) {
                document.getElementById('spinner').style.display = 'none';
                document.getElementById('successMessage').textContent = message;
                document.getElementById('successMessage').style.display = 'block';
                document.getElementById('errorMessage').style.display = 'none';
                document.getElementById('progressBar').style.width = '100%';
            }
        }

        // Initialize solver when page loads
        document.addEventListener('DOMContentLoaded', () => {
            const solver = new PoWSolver();
            solver.init();
        });
    </script>
</body>
</html>
